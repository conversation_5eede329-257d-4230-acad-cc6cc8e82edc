# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# Coverage directory used by tools like istanbul
coverage

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# node-waf configuration
.lock-wscript

# Compiled binary addons (http://nodejs.org/api/addons.html)
build/Release
.eslintcache

# Dependency directory
# https://www.npmjs.org/doc/misc/npm-faq.html#should-i-check-my-node_modules-folder-into-git
node_modules

# OSX
.DS_Store

# App packaged
release
src/main.prod.js
src/main.prod.js.map
src/renderer.prod.js
src/renderer.prod.js.map
src/style.css
src/style.css.map
dist
dll
main.js
main.js.map

.idea
npm-debug.log.*
*.css.d.ts
*.sass.d.ts
*.scss.d.ts

yarn.lock

# Environment variables
.env

node_modules
release
.vscode

