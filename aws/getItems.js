const AWS = require('aws-sdk');

const dynamo = new AWS.DynamoDB.DocumentClient();

const TableName = 'kthq';

exports.handler = async (event, context) => {
  if (event.headers['x-custom-api-key'] !== process.env.apiKey)
    return {
      statusCode: 401,
      body: 'Unauthorized',
    };

  let body = { items: [] };
  let statusCode = '200';
  const headers = {
    'Content-Type': 'application/json',
  };

  try {
    const itemIds = event.queryStringParameters.itemIds.split(',');

    const queryResults = await Promise.all(
      itemIds.map(async (itemId) =>
        dynamo
          .get({
            TableName,
            Key: {
              itemId,
            },
          })
          .promise()
      )
    );

    body.items = queryResults.map(({ Item: item }, index) =>
      Object.fromEntries(
        Object.entries(item || { itemId: itemIds[index] }).map(
          ([key, value]) => [
            key.replace(/_$/, ''), // strip trailing slash from key if present
            value,
          ]
        )
      )
    );
  } catch (err) {
    statusCode = '400';
    body = err.message;
  } finally {
    body = JSON.stringify(body);
  }

  return {
    statusCode,
    body,
    headers,
  };
};
