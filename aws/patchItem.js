const AWS = require('aws-sdk');

const dynamo = new AWS.DynamoDB.DocumentClient();

const TableName = 'kthq';

const reservedWordReplacements = ['style']; // reserved word in DynamboDB

exports.handler = async (event, context) => {
  if (event.headers['x-custom-api-key'] !== process.env.apiKey)
    return {
      statusCode: 401,
      body: 'Unauthorized',
    };

  let body;
  let statusCode = '200';
  const headers = {
    'Content-Type': 'application/json',
  };

  try {
    const { itemId } = event.pathParameters;

    const fields = JSON.parse(event.body);

    const ExpressionAttributeValues = {};
    const attributeKeys = [];

    Object.entries(fields).forEach(([key, value]) => {
      const filteredKey = reservedWordReplacements.includes(key)
        ? `${key}_`
        : key;
      ExpressionAttributeValues[`:${filteredKey}`] = value;
      attributeKeys.push(filteredKey);
    });
    const UpdateExpression = `set ${attributeKeys
      .map((key) => `${key} = :${key}`)
      .join(', ')}`;

    await dynamo
      .update({
        TableName,
        Key: {
          itemId,
        },
        UpdateExpression,
        ExpressionAttributeValues,
      })
      .promise();
  } catch (err) {
    statusCode = '400';
    body = err.message;
  } finally {
    body = JSON.stringify(body);
  }

  return {
    statusCode,
    body,
    headers,
  };
};
