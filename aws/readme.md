To deploy on AWS:

  1. create DynamoDB database
     1. primary key = `itemId` (string)
  2. create API Gateway
  3. create lambdas with corresponding API Gateway routes
     1. `GET /items` -> `getItems.js` 
     2. `PATCH /item/{itemId}` -> `patchItem.js`
     3. (PUT is deprecated)
  4. generate a random API key string to use
  5. use API string in environment variable `apiKey` in each lambda, deploy lambdas
