const AWS = require('aws-sdk');

const dynamo = new AWS.DynamoDB.DocumentClient();

const TableName = 'kthq';

exports.handler = async (event, context) => {
  if (event.headers['x-custom-api-key'] !== process.env.apiKey)
    return {
      statusCode: 401,
      body: 'Unauthorized',
    };

  let body;
  let statusCode = '200';
  const headers = {
    'Content-Type': 'application/json',
  };

  try {
    const { itemId } = event.pathParameters;

    await dynamo
      .put({
        TableName,
        Key: {
          itemId,
        },
        Item: JSON.parse(event.body),
      })
      .promise();
  } catch (err) {
    statusCode = '400';
    body = err.message;
  } finally {
    body = JSON.stringify(body);
  }

  return {
    statusCode,
    body,
    headers,
  };
};
