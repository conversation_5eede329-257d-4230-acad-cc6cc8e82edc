/* eslint-disable promise/catch-or-return */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { useSelector } from 'react-redux';
import { find, pickBy } from 'lodash';
import { useHistory } from 'react-router';
import path from 'path';
import React, { useMemo, useState, useEffect } from 'react';

import { GoogleSpreadsheet } from 'google-spreadsheet';
import { View } from './View';
import { Button } from './Button';
import { CloseButton } from './CloseButton';
import { SelectInput } from './SelectInput';
import { TextInput } from './TextInput';
import { EngravingControls } from './EngravingControls';
import { EngravingPreview } from './EngravingPreview';
import {
  selectItems,
  selectRingSpec,
  selectRingOptions,
  selectIsItemsLocalDataLoaded,
} from '../data/selectors';
import { getSettings } from '../data/settings';
import { createPdf } from '../lib/createPdf';
import { useWatchedState } from '../lib/useWatchedState';
import { openFile, maybeRenameItemFile, patchItemData } from '../data/actions';
import { createShopifyOrderFromItem } from '../data/shopifyActions';

import './ItemDetailView.global.scss';

const pk =
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    /\\n/g,
    '\n'
  );
const { clipboard } = require('electron');

export const saveOOS = async (data) => {
  try {
    const DOC_ID = '18v8_WGeI8yrRmZhjLWTHZ0ZmjzgTQdP8FJnlSMrwIpM';
    const doc = new GoogleSpreadsheet(DOC_ID);

    await doc.useServiceAccountAuth({
      client_email: '<EMAIL>',
      private_key: pk,
    });

    await doc.loadInfo(); // Load document properties and worksheets

    // Get the specific sheet by title
    const sheet = doc.sheetsByTitle['OOS App G Batch ']; // Ensure the correct sheet name
    if (!sheet) {
      throw new Error(`Sheet with title 'RingTool' not found`);
    }

    console.log(data);
    // Add a new row at the end of the sheet with specified data
    await sheet.addRow(data);

    return null;
  } catch (error) {
    return null;
  }
};

export const loadConfigFromCloud = async (size, style) => {
  try {
    const DOC_ID = '1QUuFD84Q1BcByVgVTt7BGH_K89xkykUja4_QKxlXijk'; // Spreadsheet ID from your link
    const doc = new GoogleSpreadsheet(DOC_ID);

    await doc.useServiceAccountAuth({
      client_email: '<EMAIL>',
      private_key: pk,
    });

    await doc.loadInfo(); // Load document properties and worksheets
    const { diameter } = getSettings();

    const sheet = doc.sheetsByTitle[diameter]; // Ensure the correct sheet name
    if (!sheet) {
      throw new Error(`Sheet with title 'RingTool' not found`);
    }

    // Load rows from the sheet
    const rows = await sheet.getRows();

    // Find the row with the specified size and style
    const foundRow = rows.find(
      (row) => row.Size === size.toString() && row.Style === style
    );

    if (!foundRow) {
      throw new Error(
        `No matching row found for size ${size} and style ${style}`
      );
    }

    console.log({
      style: foundRow.Style,
      size: foundRow.Size,
      diameter: foundRow.Diameter,
      xValue: foundRow['1st X-Value'],
    });
    // Return both diameter and 1st X-Value
    return {
      style: foundRow.Style,
      size: foundRow.Size,
      diameter: foundRow.Diameter,
      xValue: foundRow['1st X-Value'],
    };
  } catch (error) {
    return null; // Return null in case of error
  }
};

const isUnprintableText = (text = '') => {
  const unprintableCharacters = [
    '\\u00a9',
    '\\u00ae',
    '[\\u2000-\\u3300]',
    '\\ud83c',
    '[\\ud000-\\udfff]',
    '\\ud83d',
    '[\\ud000-\\udfff]',
    '\\ud83e',
    '[\\ud000-\\udfff]',
  ];

  const reallowedCharacters = ['♡|♥|❤|💙|💚|💛|🧡|💜|🖤', '•', '♾|∞'];

  const scrubbedText = text.replace(
    new RegExp(reallowedCharacters.join('|'), 'ig'),
    ''
  );

  return (
    scrubbedText.replace(new RegExp(unprintableCharacters.join('|', 'ig')), '')
      .length !== scrubbedText.length
  );
};

const InfoField = ({ label, value, isHighlight }) => (
  <div
    className={`ItemDetailView__infoField ${
      isHighlight ? 'ItemDetailView__highlight' : ''
    }`}
  >
    <strong>{label}</strong>: {value}
  </div>
);

const RelatedOrderLink = ({ orderId, handleClose }) => {
  return (
    <span
      onClick={() => {
        // TODO: clean up this hack
        window.nextTextFilter = orderId;
        handleClose();
      }}
    >
      {orderId}
    </span>
  );
};

const dropComputedProperties = (value, key) => key[0] !== '_';

export const ItemDetailView = ({
  match: {
    params: { itemId },
  },
}) => {
  const items = useSelector(selectItems);
  const isItemsLocalDataLoaded = useSelector(selectIsItemsLocalDataLoaded);
  const item = useMemo(() => {
    if (!itemId) return undefined;
    return find(items, { itemId });
  }, [items, itemId]);
  const history = useHistory();
  const handleClose = () => history.goBack();

  const ringOptions = useSelector(selectRingOptions);

  const [isDirty, setIsDirty] = useState(false);

  const [style, setStyle] = useWatchedState(item?.style, setIsDirty);
  const [size, setSize] = useWatchedState(item?.size, setIsDirty);
  const [diameter, setDiameter] = useState(0);
  const [xValue, setXValue] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  const [color, setColor] = useWatchedState(item?.color, setIsDirty);
  const [notes, setNotes] = useWatchedState(item?.notes || '', setIsDirty);
  const [inside, setInside] = useState(item?.inside || {});
  const [outside, setOutside] = useState(item?.outside || {});
  const [isSpinoffDisabled, setIsSpinoffDisabled] = useState(false);
  const [orderItems, setItems] = useState([]);

  const [chitchatsBatchId, setChitchatsBatchId] = useWatchedState(
    item?.chitchatsBatchId,
    setIsDirty
  );
  const [chitchatsShipmentId, setChitchatsShipmentId] = useWatchedState(
    item?.chitchatsShipmentId,
    setIsDirty
  );

  const isSaveable = !!item;
  const hasAi = !!item?.aiFileName;

  const ringSpec = useSelector((state) =>
    selectRingSpec(state, { style, size })
  );

  const handleSaveItemData = async (properties = {}) =>
    patchItemData({
      fileName: item.fileName || `${item.targetFileName}.json`,
      itemId,
      style,
      size,
      color,
      notes,
      inside: pickBy(inside, dropComputedProperties),
      outside: pickBy(outside, dropComputedProperties),
      isReady: item.isReady,
      isPrinted: item.isPrinted,
      chitchatsBatchId, // TODO: ensure trimmed (but careful not to trim null)
      chitchatsShipmentId, // TODO: ensure trimmed (but careful not to trim null)
      ...properties,
    });

  useEffect(() => {
    try {
      setItems(item.shopifyOrder.line_items);
    } catch (e) {}

    const checkConfig = async () => {
      try {
        setIsLoading(true);
        const sizeNumber = parseInt(size.match(/\d+/)[0], 10);
        const result = await loadConfigFromCloud(sizeNumber, style);
        if (result) {
          setDiameter(result?.diameter ?? 0);
          setXValue(result?.xValue ?? 0);
        }
      } catch (error) {
        console.error('Error checking config:', error.message);
      } finally {
        setIsLoading(false);
      }
    };

    if (size && style) {
      checkConfig();
    }
  }, [size, style]);

  const handleCreateSpinoffOrder = async () => {
    setIsSpinoffDisabled(true);
    const createdItem = await createShopifyOrderFromItem({ item });

    // save this order data
    const existingNotesTrimmed = notes.trim();
    const newNotes = `${existingNotesTrimmed}${
      existingNotesTrimmed ? '\n' : ''
    }[Child: ${createdItem.orderId}]`;
    await handleSaveItemData({ notes: newNotes });

    // save new spin-off order data
    await patchItemData({
      fileName: `${createdItem.targetFileName}.json`,
      itemId: createdItem.itemId,
      style,
      size,
      color,
      notes: `[Parent: ${item.orderId}${
        item.orderItemNumberOfTotal ? ` ${item.orderItemNumberOfTotal}` : ''
      }]\n`,
      inside: pickBy(inside, dropComputedProperties),
      outside: pickBy(outside, dropComputedProperties),
    });

    // navigate to new spin-off order
    window.setTimeout(() => history.replace(`/`), 0); // let store dispatches apply
    window.setTimeout(
      () => history.replace(`/item/${createdItem.itemId}`),
      500
    );
  };

  const handlePrintedChange = async (isPrinted) => {
    handleSaveItemData({ isPrinted });
    maybeRenameItemFile(item);
  };

  const handleOpenAiFile = () => {
    const { filesBasePath } = getSettings();
    openFile(path.join(filesBasePath, `${item.aiFileName}`));
  };

  // problem detection
  const insideProblems = [];
  const outsideProblems = [];
  const generalProblems = [];
  const warnings = [];

  if (!style) generalProblems.push('Style not set.');
  if (!size) generalProblems.push('Size not set.');
  if (!color && item) generalProblems.push('Color not set.');
  if (!ringSpec) generalProblems.push('Ring spec not found.');

  if (!hasAi) {
    if (inside.text && inside.design)
      insideProblems.push('Inside has design and text.');
    if (inside._isOversize) insideProblems.push('Inside is oversize.');
    if (isUnprintableText(inside.text))
      warnings.push('Inside text may be unprintable.');

    if (outside.text && outside.design)
      outsideProblems.push('Outside has design and text.');
    if (outside._isOversize) outsideProblems.push('Outside is oversize.');
    if (isUnprintableText(outside.text))
      warnings.push('Outside text may be unprintable.');
  }

  const relatedOrderIds = useMemo(
    () => notes.match(/\bE\d{10}\b|\bKT\d{6}\b/g) || [],
    [notes]
  );

  const allProblems = [
    ...insideProblems,
    ...outsideProblems,
    ...generalProblems,
  ];
  const hasProblem = allProblems.length > 0;
  const isInsideUnprintable =
    insideProblems.length + generalProblems.length > 0 ||
    (!inside.text && !inside.design) ||
    hasAi;
  const isOutsideUnprintable =
    outsideProblems.length + generalProblems.length > 0 ||
    (!outside.text && !outside.design) ||
    hasAi;

  return (
    <View>
      <div className="ItemDetailView">
        <div className="ItemDetailView__header">
          <CloseButton />
          {item ? (
            <>
              <h2>
                {item.orderId}: {item.name} {item.orderItemNumberOfTotal}{' '}
                <InfoField label="Date" value={item.createdTimeLocal} />
              </h2>
              <div className="spacer" />
              <Button
                onClick={handleCreateSpinoffOrder}
                label="Create Spin-off Child"
                disabled={isSpinoffDisabled}
              />
              {item.id && (
                <>
                  {item.orderId.startsWith('KT') ? (
                    <Button
                      style={{
                        marginLeft: '10px',
                        color: 'green',
                        fontWeight: 'bold',
                      }}
                      onClick={() => {
                        window.open(
                          `${
                            process.env.SHOPIFY_ADMIN_URL ||
                            'https://admin.shopify.com/store/knotheoryrings'
                          }/orders/${item.id}`,
                          '_blank'
                        );
                      }}
                      label="Open Shopify"
                    />
                  ) : null}
                </>
              )}
            </>
          ) : (
            <h2>Designer</h2>
          )}
        </div>
        {item && (
          <div className="ItemDetailView__detail">
            <div className="ItemDetailView__detailFields">
              {item.message && (
                <InfoField
                  label="Message"
                  value={item.message}
                  isHighlight={!item.isReady}
                />
              )}

              {item.customFields.map(({ label, value }, index) =>
                value ? (
                  <div
                    key={label}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: '6px', // Alternating row colors
                    }}
                  >
                    {((label.includes('Inside') && label.includes('Eng')) ||
                      label.includes('Custom')) && (
                      <span
                        style={{
                          width: '12px',
                          height: '12px',
                          backgroundColor: '#12d8e8',
                          borderRadius: '10%',
                          marginRight: '6px',
                        }}
                      />
                    )}
                    {((label.includes('Outside') && label.includes('Eng')) ||
                      label.includes('Personalization')) && (
                      <span
                        style={{
                          width: '12px',
                          height: '12px',
                          backgroundColor: '#0ab505',
                          borderRadius: '10%',
                          marginRight: '6px',
                        }}
                      />
                    )}
                    {label.includes('Bake') && (
                      <span
                        style={{
                          width: '12px',
                          height: '12px',
                          backgroundColor: '#e8c512',
                          borderRadius: '10%',
                          marginRight: '6px',
                        }}
                      />
                    )}
                    <span
                      style={{
                        marginRight: '6px',
                        whiteSpace: 'nowrap',
                        fontWeight: 'bold',
                      }}
                    >
                      {label
                        .replace('(click on ring to select)', '')
                        .replace('Engraving?', '')}
                      :
                    </span>
                    <span style={{ whiteSpace: 'nowrap' }}>{value}</span>
                  </div>
                ) : null
              )}

              <div>
                <InfoField
                  label="Quantity"
                  value={item.quantity}
                  isHighlight={item.isReady && item.quantity > 1}
                />
              </div>

              <div>
                <span
                  style={{
                    whiteSpace: 'nowrap',
                    color: '#f0eded',
                    fontSize: '0.6em',
                    paddingTop: '6px',
                  }}
                >
                  {item.sku}
                </span>
              </div>
            </div>

            <div className="ItemDetailView__detailChitchats">
              <div>
                <div>Batch ID:</div>
                <div>
                  <TextInput
                    value={chitchatsBatchId}
                    onChange={setChitchatsBatchId}
                  />
                </div>
                <div>Shipment ID:</div>
                <div>
                  <TextInput
                    value={chitchatsShipmentId}
                    onChange={setChitchatsShipmentId}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
        <div className="ItemDetailView__preview">
          <div className="ItemDetailView__ringSpecControls">
            <SelectInput
              value={style}
              onChange={setStyle}
              options={ringOptions.styles}
              className="ItemDetailView__ringOptionSelect"
              disabled={item?.isShipped}
            />
            <SelectInput
              value={size}
              onChange={setSize}
              options={ringOptions.sizes}
              className="ItemDetailView__ringOptionSelect"
              disabled={item?.isShipped}
            />
            <SelectInput
              value={color}
              onChange={setColor}
              options={ringOptions.colors}
              className="ItemDetailView__ringOptionSelect"
              disabled={item?.isShipped}
            />
          </div>

          <div
            className="grid"
            style={{ display: 'flex', gap: '20px', marginTop: '20px' }}
          >
            {ringSpec && !hasAi && (
              <div
                style={{
                  flex: 1,
                  borderRadius: '10px',
                  backgroundColor: '#1c2833', // Light background color for the engraving section
                  padding: '20px',
                  boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
                  overflow: 'hidden', // Ensures rounded corners work well with inner content
                }}
              >
                <div
                  className="ItemDetailView__previewTitle"
                  style={{ marginBottom: '10px' }}
                >
                  <h3 style={{ margin: 0 }}>Outside</h3>
                  <div className="spacer" />
                  <div>
                    <Button
                      className="ItemDetailView__printButton"
                      disabled={isOutsideUnprintable}
                      onClick={async () => {
                        try {
                          await createPdf({
                            svg: outside._svg,
                            fileName: item?.fileName || 'designer',
                            side: 'outside',
                            surfaceWidth: ringSpec.outside.surfaceWidth,
                            surfaceLength: ringSpec.outside.surfaceLength,
                            power: ringSpec.outside.power,
                          });
                        } catch (error) {
                          console.error('Error creating outside PDF:', error);
                        }
                      }}
                      label="Print Outside"
                    />
                  </div>
                </div>
                <EngravingControls
                  value={outside}
                  onChange={setOutside}
                  disabled={item?.isShipped}
                  onDirty={setIsDirty}
                />
                <EngravingPreview
                  side="outside"
                  surface={ringSpec.outside}
                  engraving={outside}
                  setEngraving={setOutside}
                />
                <div
                  className="ItemDetailView__previewTitle"
                  style={{ marginTop: '40px', marginBottom: '10px' }}
                >
                  <h3 style={{ margin: 0 }}>Inside</h3>
                  <div className="spacer" />

                  <div>
                    <Button
                      className="ItemDetailView__printButton"
                      disabled={isInsideUnprintable}
                      onClick={async () => {
                        try {
                          await createPdf({
                            svg: inside._svg,
                            fileName: item?.fileName || 'designer',
                            side: 'inside',
                            surfaceWidth: ringSpec.inside.surfaceWidth,
                            surfaceLength: ringSpec.inside.surfaceLength,
                            pdfSurfaceLength: ringSpec.outside.surfaceLength,
                            power: ringSpec.inside.power,
                          });
                        } catch (error) {
                          console.error('Error creating inside PDF:', error);
                        }
                      }}
                      label="Print Inside"
                    />
                  </div>
                </div>
                <EngravingControls
                  value={inside}
                  onChange={setInside}
                  customOnly
                  disabled={item?.isShipped}
                  onDirty={setIsDirty}
                />
                <EngravingPreview
                  side="inside"
                  surface={ringSpec.inside}
                  engraving={inside}
                  setEngraving={setInside}
                />
              </div>
            )}

            <div
              style={{
                flex: 0.75,
                backgroundColor: '#1c2833',
                borderRadius: '10px',
                padding: '20px',
                boxShadow: '0 4px 10px rgba(0, 0, 0, 0.1)',
                overflow: 'hidden',
                display: 'flex',
                fontSize: '18px',
                color: '#f7f9f9',
              }}
            >
              <div className="ItemDetailView__detailNotes">
                <div>
                  <strong>Note</strong>
                  <br />
                  <textarea
                    rows="4"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    style={{
                      padding: '10px',
                      marginTop: '10px',
                      borderRadius: '5px',
                      border: '1px solid #ddd',
                    }}
                  />
                </div>

                {relatedOrderIds.map((relatedOrderId) => (
                  <RelatedOrderLink
                    key={relatedOrderId}
                    orderId={relatedOrderId}
                    handleClose={handleClose}
                  />
                ))}

                <div style={{ marginTop: '20px' }}>
                  {orderItems.length > 0 && (
                    <div>
                      <h4>Order Items</h4>
                      <div style={{ maxHeight: '150px', overflowY: 'auto' }}>
                        {orderItems.map((item, index) => (
                          <div
                            key={index}
                            style={{
                              display: 'flex',
                              alignItems: 'center',
                              padding: '10px 0',
                              borderBottom: '1px solid #eee',
                              marginBottom: '5px',
                              marginRight: '5px',
                              fontSize: '12px',
                            }}
                          >
                            <div style={{ flex: 1 }}>{item.sku}</div>
                            <div>Quantity: {item.quantity}</div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {hasAi && (
            <Button
              className="ItemDetailView__openAiButton"
              onClick={handleOpenAiFile}
              label="Open AI"
            />
          )}
          <div className="ItemDetailView__problems">
            {[...allProblems, ...warnings].join(' ')}
          </div>
          {item && (
            <div>
              <div>
                {item.isReady ? (
                  <span className="ItemDetailView__badge ItemDetailView__badge--isReady">
                    Design Ready
                  </span>
                ) : (
                  <span className="ItemDetailView__badge ItemDetailView__badge--notIsReady">
                    Design Not Ready
                  </span>
                )}
                &nbsp;
                {item.isPrinted ? (
                  <span className="ItemDetailView__badge ItemDetailView__badge--isPrinted">
                    Printed
                  </span>
                ) : (
                  <span className="ItemDetailView__badge ItemDetailView__badge--notIsPrinted">
                    Not Printed
                  </span>
                )}
              </div>
              <Button
                className="ItemDetailView__button"
                onClick={() => {
                  handleSaveItemData().then(handleClose);
                }}
                label="Save"
                disabled={!isSaveable || !isDirty}
              />
              <Button
                className="ItemDetailView__button"
                disabled={(!item.isReady && hasProblem) || !isSaveable}
                onClick={() => {
                  handleSaveItemData({ isReady: !item.isReady }).then(
                    handleClose
                  );
                }}
                label={
                  item.isReady ? 'Mark Design NOT Ready' : 'Mark Design Ready'
                }
              />
              <Button
                className="ItemDetailView__button"
                disabled={
                  (!item.isReady && !item.isPrinted) || !isItemsLocalDataLoaded
                }
                onClick={() => {
                  handlePrintedChange(!item.isPrinted).then(handleClose);
                }}
                label={item.isPrinted ? 'Mark NOT Printed' : 'Mark Printed'}
              />
            </div>
          )}

          {ringSpec && (
            <div className="ItemDetailView__scrollContainer">
              <div className="ItemDetailView__section">
                <div className="ItemDetailView__copyValuesContainer">
                  {isLoading ? (
                    <div>Loading...</div>
                  ) : (
                    <>
                      <Button
                        className="ItemDetailView__copyValues"
                        disabled={!ringSpec}
                        onClick={() => {
                          clipboard.writeText(xValue);
                        }}
                        label={`Copy X Value: ${xValue}`}
                      />
                      <Button
                        className="ItemDetailView__copyValues"
                        disabled={!ringSpec}
                        onClick={() => {
                          clipboard.writeText(diameter);
                        }}
                        label={`Copy Diameter: ${diameter}`}
                      />
                      <Button
                        className="ItemDetailView__copyValues"
                        disabled={!ringSpec}
                        onClick={() => {
                          clipboard.writeText(xValue);
                          setTimeout(() => {
                            clipboard.writeText(diameter);
                          }, 1000);
                        }}
                        label="Copy Both"
                      />
                    </>
                  )}
                </div>
              </div>

              {item && (
                <div>
                  <p>
                    Put your notes, and then click the OOS button. This will
                    create a row in the Excel file with your notes. It will
                    automatically get order info such as the customers name,
                    ring details, quantity, email, order ID, and date.
                  </p>

                  <Button
                    className="ItemDetailView__oos"
                    onClick={() => {
                      console.log(`${item.isReady} || ${item.notes}`);
                      const oosItems = { ...item };
                      oosItems.isReady = false;
                      oosItems.notes = `${oosItems.notes ?? ''}\n${notes}`;

                      saveOOS({
                        Date: oosItems.createdTimeLocal,
                        'Customer Name': oosItems.name,
                        'Market ID': oosItems.orderId,
                        Notes: `${oosItems.notes}\n${oosItems.quantity} × ${style}-${size}-${color}`,
                        'Status (Refunded? Waiting for customer reply?)': '',
                        Shipped: '',
                        'Email Address': oosItems.client_email,
                      });

                      handleSaveItemData({ isReady: oosItems.isReady }).then(
                        handleClose
                      );
                    }}
                    label="OOS"
                  />
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </View>
  );
};
