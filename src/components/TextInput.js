/* eslint-disable import/prefer-default-export */
import React, { useCallback, forwardRef, useEffect } from 'react';

export const TextInput = ({
  value = '',
  onChange,
  placeholder,
  size = 30,
  ...restProps
}) => {
  const handleChange = useCallback(
    (e) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  return (
    <input
      className="TextInput"
      type="text"
      value={value}
      onChange={handleChange}
      placeholder={placeholder}
      size={size}
      {...restProps}
    />
  );
};
