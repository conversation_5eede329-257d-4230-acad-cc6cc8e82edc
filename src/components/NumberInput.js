/* eslint-disable import/prefer-default-export */
import React, { useCallback } from 'react';

export const NumberInput = ({
  className = '',
  value = '',
  defaultValue,
  onChange,
  ...restProps
}) => {
  const handleChange = useCallback(
    (e) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  return (
    <input
      className={`NumberInput ${className}`}
      type="number"
      value={value || defaultValue}
      onChange={handleChange}
      {...restProps}
    />
  );
};
