import React from 'react';

export const SelectInput = ({
  value,
  onChange,
  options,
  className,
  ...rest
}) => {
  const handleChange = (e) => {
    onChange(e.target.value);
  };

  return (
    <select
      className={className}
      value={value}
      onChange={handleChange}
      {...rest}
    >
      {options.map(({ label, value: optionValue }) => (
        <option key={`${label}:${String(optionValue)}`} value={optionValue}>
          {label}
        </option>
      ))}
    </select>
  );
};
