import React, { useEffect } from 'react';
import { AgGridReact } from 'ag-grid-react';
import { useHistory } from 'react-router-dom';
import { useSelector } from 'react-redux';
import {
  selectInlayDesigns,
  selectInlayGoldDesigns,
  selectInlaySilverDesigns,
} from '../data/selectors';

import './Items.global.scss';

const checkmarkFormatter = ({ value }) => (value ? '✔' : '');

export const isMixedOrder = (data) => {
  let transactions = [];

  if (data.itemId?.toLowerCase().includes('kt')) {
    // Shopify
    transactions = data.shopifyOrder?.line_items || [];
  } else {
    // Etsy
    transactions = data.etsyReceipt?.transactions || [];
  }

  if (!Array.isArray(transactions) || transactions.length === 0) {
    return false;
  }

  const containsEngraved = transactions.some(
    (t) =>
      t.title?.toLowerCase().includes('engraved') || t.sku?.startsWith('G-')
  );
  const containsNonEngraved = transactions.some(
    (t) =>
      !t.title?.toLowerCase().includes('engraved') && !t.sku?.startsWith('G-')
  );

  return containsEngraved && containsNonEngraved;
};

export const hasErrorDesign = (data) => {
  const outsideDesign = data.outside?.design?.toLowerCase() || '';
  const insideDesign = data.inside;

  return data.customFields.some(({ label, value }) => {
    // Check for "Outside" design issue
    if (
      label.includes('Outside') &&
      (!outsideDesign || outsideDesign.trim() === '')
    ) {
      return true;
    }

    // Check for "Inside" design issue
    if (
      label.includes('Inside') &&
      value &&
      (!insideDesign || String(insideDesign).trim() === '')
    ) {
      console.warn('hasErrorDesign');
      return true;
    }

    return false; // Default case when no error is found
  });
};

const rowClassRules = {
  'Items__row--isCancelled': (params) => params.data.isCancelled,
};

const designTextGetter =
  (field) =>
  ({ data }) => {
    const design = data[field]?.design || '';
    const text = data[field]?.text || '';
    return design ? `«${design}»` : text;
  };

const circleIconsGetter = ({ data }) =>
  [
    isMixedOrder(data) ? '🟢' : '',
    data.quantity > 1 ? '🔵' : '',
    // hasErrorDesign(data) ? '🔴' : '',
  ]
    .filter(Boolean)
    .join(' ');

const getRowStyle = (params) => {
  const { data } = params;

  return null; // Default row style
};

export const Items = ({
  rows,
  gridApis,
  setGridApis,
  onSelectedRowsChanged,
  onDisplayedRowsChanged,
  searchText,
}) => {
  const inlayDesigns = useSelector(selectInlayDesigns);
  const inlayGoldDesigns = useSelector(selectInlayGoldDesigns);
  const inlaySilverDesigns = useSelector(selectInlaySilverDesigns);

  const history = useHistory();
  const mixedOrderChecker = ({ data }) => {
    if (!isMixedOrder(data)) {
      return null;
    }

    let transactions = [];
    if (data.itemId?.toLowerCase().includes('kt')) {
      transactions = data.shopifyOrder?.line_items || [];
    } else {
      transactions = data.etsyReceipt?.transactions || [];
    }

    const skuList = transactions
      .filter(
        (t) =>
          !t.title?.toLowerCase().includes('engraved') &&
          !t.sku?.startsWith('G-')
      )
      .map((t) => t.sku)
      .filter((sku) => sku && sku !== data.sku);

    if (skuList.length === 0) {
      return 'Mix order';
    }

    return `Mix order: ${skuList.join(', ')}`;
  };

  const inlayIncludedGetter = ({ data }) => {
    const notes = data.notes?.toLowerCase() || '';
    const sku = data.sku?.toLowerCase() || '';
    const outsideDesign = data.outside?.design?.toLowerCase() || '';
    const insideDesign = data.inside;

    let goldDesign = false;
    let goldDesignsWithInside = false;

    let isInsideDesignValid = false;
    let isOutsideDesignValid = false;
    try {
      goldDesign =
        Object.values(inlayDesigns).some(
          (design) => design?.design?.toLowerCase() === outsideDesign
        ) &&
        (data.style?.toLowerCase() === 'cf4' ||
          data.style?.toLowerCase() === 'cf6');
    } catch (_) {
      console.log(_);
    }

    try {
      isOutsideDesignValid =
        (outsideDesign != null &&
          typeof outsideDesign === 'string' &&
          outsideDesign.trim().length > 0) ||
        (typeof outsideDesign === 'object' &&
          Object.keys(outsideDesign).length > 0);

      isInsideDesignValid =
        (insideDesign != null &&
          typeof insideDesign === 'string' &&
          insideDesign.trim().length > 0) ||
        (typeof insideDesign === 'object' &&
          Object.keys(insideDesign).length > 0);

      goldDesignsWithInside =
        Object.values(inlayGoldDesigns).some(
          (design) => design?.design?.toLowerCase() === outsideDesign
        ) &&
        isInsideDesignValid &&
        goldDesign;
    } catch (_) {}

    let includesInlay =
      notes.includes('inlay') ||
      notes.includes('outside si') ||
      notes.includes('inside si') ||
      notes.includes('outside gi') ||
      notes.includes('inside gi') ||
      sku.includes('inlay') ||
      sku.includes('goldink') ||
      sku.includes('silverink') ||
      goldDesign ||
      includesClear;

    let includesInside =
      notes.includes('inside') ||
      sku.includes('inside') ||
      sku.includes('inkio') ||
      goldDesignsWithInside;

    let includesOutside =
      notes.includes('outside') ||
      sku.includes('outside') ||
      sku.includes('inkio') ||
      goldDesign;

    let includesGold =
      notes.includes('gold') ||
      notes.includes('outside gi') ||
      notes.includes('inside gi') ||
      sku.includes('goldink') ||
      sku.includes('-gs') ||
      sku.includes('gsfili') ||
      sku.includes('-gsfili-') ||
      goldDesign;

    let includesSilver =
      notes.includes('silver') ||
      notes.includes('outside si') ||
      notes.includes('inside si') ||
      sku.includes('silverink') ||
      sku.includes('-ss') ||
      sku.includes('ssfili') ||
      sku.includes('-ssfili-');

    let includesClear =
      sku.includes('-cs') ||
      sku.includes('csfili') ||
      sku.includes('-csfili-') ||
      sku.includes('csclad') ||
      notes.includes('clear silicone') ||
      notes.includes('no inlay');

    let result = '';

    if (data.inkColor) {
      includesSilver = data.inkColor === 'silver';
      includesGold = data.inkColor === 'gold';
      includesClear = data.inkColor === 'clear' || data.inkColor === 'none';

      if (includesGold || includesSilver || includesClear) {
        if (isOutsideDesignValid) {
          includesOutside = true;
        }
        if (isInsideDesignValid) {
          includesInside = true;
        }

        includesInlay = true;
      }
    }

    if (includesInlay) {
      if (includesInside) {
        if (includesSilver) {
          result += 'Inside ⚪';
        } else if (includesGold) {
          result += 'Inside 🟡';
        } else if (includesClear) {
          result += 'Inside ⚫'; // Clear/no inlay represented by black circle
        }
      }

      if (includesOutside) {
        if (result.length > 0) {
          result += ' + ';
        }
        if (includesSilver) {
          result += 'Outside ⚪';
        } else if (includesGold) {
          result += 'Outside 🟡';
        } else if (includesClear) {
          result += 'Outside ⚫'; // Clear/no inlay represented by black circle
        }
      }
    }

    return result;
  };

  const gridOptions = {
    suppressCellSelection: true,
    defaultColDef: {
      sortable: true,
      resizable: true,
    },
    columnDefs: [
      {
        headerName: 'Order',
        children: [
          {
            headerName: '!',
            field: 'circles',
            valueGetter: circleIconsGetter,
            cellStyle: { fontSize: '10px' },
          },
          {
            headerName: 'ID',
            field: 'orderId',
          },
          {
            headerName: '#/#',
            field: 'orderItemNumberOfTotal',
          },
          {
            headerName: 'Created At',
            field: 'createdTimeLocal',
          },
          {
            headerName: 'Message',
            field: 'message',
          },
          {
            headerName: 'Shipped',
            field: 'isShipped',
            valueFormatter: checkmarkFormatter,
          },
          {
            headerName: 'Inlay',
            field: 'inlay',
            valueGetter: inlayIncludedGetter,
          },
          {
            headerName: 'Mixed',
            field: 'mixed',
            valueGetter: mixedOrderChecker,
          },
        ],
      },
      {
        headerName: 'Customer',
        children: [
          {
            field: 'name',
          },
          {
            headerName: 'Email',
            field: 'email',
          },
        ],
      },
      {
        headerName: 'Product',
        children: [
          {
            headerName: 'Engrave Order',
            field: 'isEngraved',
            valueFormatter: checkmarkFormatter,
          },
          {
            field: 'quantity',
          },
          {
            headerName: 'SKU',
            field: 'sku',
          },
          {
            field: 'style',
          },
          {
            field: 'size',
          },
          {
            field: 'color',
          },
          {
            field: 'outside',
            valueGetter: designTextGetter('outside'),
          },
          {
            field: 'inside',
            valueGetter: designTextGetter('inside'),
          },
          {
            headerName: 'Ready To Print',
            field: 'isReady',
            valueFormatter: checkmarkFormatter,
          },
          {
            headerName: 'Printed',
            field: 'isPrinted',
            valueFormatter: checkmarkFormatter,
          },
          {
            headerName: 'AI File',
            field: 'aiFileName',
          },
        ],
      },
      {
        headerName: 'Notes',
        children: [
          {
            headerName: 'Notes',
            field: 'notes',
          },
        ],
      },
    ],
    getRowStyle,
  };

  const saveState = () => {
    window.localStorage.setItem(
      'gridColumnState',
      JSON.stringify(gridApis.columnApi.getColumnState())
    );
  };

  const restoreState = (columnApi) => {
    const gridColumnState = window.localStorage.getItem('gridColumnState');
    if (!gridColumnState) return;

    (columnApi || gridApis.columnApi).applyColumnState({
      state: JSON.parse(gridColumnState),
      applyOrder: true,
    });
  };

  const handleGridReady = (params) => {
    setGridApis(params);
    restoreState(params.columnApi);
  };

  const handleOnSelectionChanged = ({ api }) => {
    onSelectedRowsChanged(api.getSelectedRows());
  };

  const handleModelUpdated = (params) => {
    if (!onDisplayedRowsChanged) return;
    onDisplayedRowsChanged(
      params.api.getModel().rowsToDisplay.map((row) => row.data)
    );
    handleOnSelectionChanged(params);
  };

  useEffect(() => {
    if (!gridApis) return;
    gridApis.api.setQuickFilter(searchText);
  }, [gridApis, searchText]);

  return (
    <div className="Items ag-theme-balham-dark">
      <AgGridReact
        rowData={rows}
        immutableData
        gridOptions={gridOptions}
        rowSelection="multiple"
        onGridReady={handleGridReady}
        onColumnResized={saveState}
        onColumnMoved={saveState}
        onSelectionChanged={handleOnSelectionChanged}
        onModelUpdated={handleModelUpdated}
        onRowDoubleClicked={({ data: { itemId } }) => {
          history.push(`/item/${itemId}`);
        }}
        rowClassRules={rowClassRules}
        getRowNodeId={({ itemId }) => itemId}
        style={{ width: '100%', height: '100%' }}
      />
    </div>
  );
};
