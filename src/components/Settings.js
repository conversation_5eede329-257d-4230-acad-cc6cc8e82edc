/* eslint-disable import/prefer-default-export */
import React, { useState, useEffect } from 'react';
import { Link, Redirect } from 'react-router-dom';

import { View } from './View';
import { TextInput } from './TextInput';
import { Button } from './Button';
import { CloseButton } from './CloseButton';
import { settings, getSettings, writeSettings, PKCE } from '../data/settings';

import './Settings.global.scss';

export const Settings = () => {
  const [redirect, setRedirect] = useState();
  const [values, setValues] = useState(getSettings());

  if (redirect) return <Redirect to={redirect} />;

  // const authUrl = `
  // https://www.etsy.com/oauth/connect?
  // response_type=code&
  // redirect_uri=${values.authRedirectUri}&
  // scope=transactions_r%20listings_r&
  // client_id=${values.api<PERSON>ey}&
  // state=state0001&
  // code_challenge=${PKCE.challenge}&
  // code_challenge_method=S256
  // `.replace(/\n| /g, '');

  return (
    <View>
      <div className="Settings">
        <div className="Settings__header">
          <CloseButton />
          <h2>Settings</h2>
        </div>
        <Button
          onClick={() => window.localStorage.removeItem('gridColumnState')}
          label="Reset Items Data Grid"
        />
        <table>
          <tbody>
            {settings.map(({ name, label, hint, readOnly }) => {
              return (
                <tr key={name}>
                  <th>{label}</th>
                  <td>
                    {readOnly ? (
                      <span>{values[name]}</span>
                    ) : (
                      <TextInput
                        value={values[name]}
                        onChange={(value) => {
                          const newValues = { ...values, [name]: value };
                          setValues(newValues);
                          writeSettings(newValues);
                        }}
                        size={80}
                      />
                    )}
                  </td>
                  <td>{hint}</td>
                </tr>
              );
            })}
          </tbody>
        </table>
        {/* <a href={authUrl} target="_blank" rel="noreferrer">
          Authorize
        </a> */}
        {/* <Button onClick={() => requestAuthToken()} label="Get Token" /> */}
      </div>
    </View>
  );
};
