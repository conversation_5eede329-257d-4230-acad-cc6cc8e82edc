/* eslint-disable react/no-danger */
import React, { useEffect, useRef } from 'react';
import { noop } from 'lodash';

import { convertUnitToPx } from '../lib/utils';

import './SvgPreview.global.scss';

const viewBoxHeightMm = 7;
const viewBoxWidthMm = 100;

export const SvgPreview = ({
  id,
  engravingContent,
  engravingScaleX,
  engravingScaleY = 1,
  otherContent,
  onEngravingLengthChange = noop,
  onPrintReady = noop,
  surfaceLength,
  surfaceWidth,
}) => {
  const width = convertUnitToPx.mm(viewBoxWidthMm);
  const height = convertUnitToPx.mm(viewBoxHeightMm);

  const svgProps = {
    version: '1.1',
    xmlns: 'http://www.w3.org/2000/svg',
    xmlnsXlink: 'http://www.w3.org/1999/xlink',
    x: '0px',
    y: '0px',
    viewBox: `0 0 ${width} ${height}`,
    // width: width,
    // height: height,
  };

  const engravingContentRef = useRef();

  useEffect(() => {
    onEngravingLengthChange(engravingContentRef.current.getBBox().width);
  }, [engravingContent]);

  const otherContentTransform = `translate(${width / 2} ${height / 2})`;
  const engravingContentDisplayTransform = `translate(${width / 2} ${
    height / 2
  }) scale(${engravingScaleX} ${engravingScaleY})`;
  const engravingContentPrintTransform = `translate(${surfaceLength / 2} ${
    surfaceWidth / 2
  }) scale(${engravingScaleX} ${engravingScaleY})`;

  const printReadySvg = `<g transform="${engravingContentPrintTransform}">${engravingContent}</g>`;

  useEffect(() => {
    onPrintReady(printReadySvg);
  }, [printReadySvg]);

  return (
    <div className="SvgPreview" id={id}>
      <svg {...svgProps}>
        <g
          dangerouslySetInnerHTML={{ __html: otherContent }}
          transform={otherContentTransform}
        />
        <g
          ref={engravingContentRef}
          dangerouslySetInnerHTML={{ __html: engravingContent }}
          transform={engravingContentDisplayTransform}
        />
      </svg>
    </div>
  );
};
