/* eslint-disable jsx-a11y/alt-text */
import React from 'react';
import { Link } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { isFinite } from 'lodash';

import { Button } from './Button';
import { loadConfigFromCloud } from '../data/actions';
import { selectIsWorking } from '../data/selectors';
import { getSettings } from '../data/settings';
import logo from '../../assets/kt-logo-white.png';
import loading from '../../assets/loading.gif';

import './Header.global.scss';

export const Header = () => {
  const { configBasePath, zoomPercent = '100' } = getSettings();
  const isWorking = useSelector(selectIsWorking);

  const isZoomPercentValid =
    isFinite(Number(zoomPercent)) &&
    Number(zoomPercent) >= 50 &&
    Number(zoomPercent) <= 150;

  return (
    <>
      {isZoomPercentValid && (
        <style
          dangerouslySetInnerHTML={{
            __html: `html { font-size: ${zoomPercent}% }`,
          }}
        />
      )}
      <header className="Header">
        <img src={logo} className="Header__logo" />
        {isWorking && <img src={loading} className="Header__loading" />}
        <div className="spacer" />
        <Link to="/designer">Designer</Link>
        <Link to="/settings">Settings</Link>
        <Button
          onClick={() => {
            loadConfigFromCloud();
          }}
          label="Reload Config"
        />
      </header>
    </>
  );
};
