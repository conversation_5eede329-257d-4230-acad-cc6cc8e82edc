/* eslint-disable import/prefer-default-export */
import React, { useCallback, forwardRef, useEffect } from 'react';

export const DateInput = ({ value = null, onChange, ...restProps }) => {
  const handleChange = useCallback(
    (e) => {
      onChange(e.target.value);
    },
    [onChange]
  );

  return (
    <input
      className="DateInput"
      type="date"
      value={value}
      onChange={handleChange}
      {...restProps}
    />
  );
};
