/* eslint-disable jsx-a11y/control-has-associated-label */
import React from 'react';
import { useSelector } from 'react-redux';

import { TextInput } from './TextInput';
import { NumberInput } from './NumberInput';
import { SelectInput } from './SelectInput';
import { selectDesignSpecs } from '../data/selectors';

import './EngravingControls.global.scss';

const fontOptions = [
  {
    value: 'Georgia;Bold*',
    label: 'Georgia Bold (auto Italic)',
  },
  {
    value: 'Georgia;Bold',
    label: 'Georgia Bold',
  },
  {
    value: 'Georgia;BoldItalic',
    label: 'Georgia Bold Italic',
  },
];

const baseDesignOptions = [
  {
    value: '',
    label: '(Text)',
  },
];

export const EngravingControls = ({
  value = {},
  onChange,
  customOnly,
  disabled,
  onDirty,
}) => {
  const designSpecs = useSelector(selectDesignSpecs);
  const designOptions = baseDesignOptions.concat(
    designSpecs.map(({ code, name }) => ({
      value: code,
      label: name,
    }))
  );

  /*
    value: {
      text,
      fontFamily,
      design,
    }
  */
  const onTextChange = (text) => {
    onChange({
      ...value,
      text,
    });
    onDirty(true);
  };

  const onFontFamilyChange = (fontFamily) => {
    onChange({
      ...value,
      fontFamily,
    });
    onDirty(true);
  };

  const onScaleChange = (scale) => {
    onChange({
      ...value,
      scale,
    });
    onDirty(true);
  };

  const onDesignChange = (design) => {
    onChange({
      ...value,
      design,
    });
    onDirty(true);
  };

  return (
    <div className="EngravingControls">
      <SelectInput
        value={value.design}
        onChange={onDesignChange}
        options={designOptions}
        disabled={disabled || customOnly}
      />
      <SelectInput
        value={value.fontFamily}
        onChange={onFontFamilyChange}
        options={fontOptions}
        disabled={disabled}
      />
      <NumberInput
        className="NumberInput--scale"
        value={value.scale}
        defaultValue={1.0}
        onChange={onScaleChange}
        step="0.01"
        placeholder="Scale"
        list="scale-options"
        disabled={disabled}
      />
      <TextInput
        value={value.text}
        onChange={onTextChange}
        size={50}
        disabled={disabled}
      />
    </div>
  );
};
