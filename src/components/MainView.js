import React, { useRef, useEffect, useState, useMemo } from 'react';
import { useSelector } from 'react-redux';

import { filter, uniqBy } from 'lodash';
import { View } from './View';
import { selectItems, selectIsWorking } from '../data/selectors';
import { scanForFiles, getItems, getOrderItems } from '../data/actions';
import { Items } from './Items';
import { DateInput } from './DateInput';
import { SelectInput } from './SelectInput';
import { TextInput } from './TextInput';
import { Button } from './Button';

import './MainView.global.scss';

// Helper function to calculate date difference in days
const getDateDifferenceInDays = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  const timeDiff = Math.abs(end - start);
  return Math.ceil(timeDiff / (1000 * 3600 * 24)); // Difference in days
};

const useDebounce = (input, delay, initial) => {
  const debounceTimerRef = useRef();
  const [debounced, setDebounced] = useState(initial);
  useEffect(() => {
    window.clearTimeout(debounceTimerRef.current);
    debounceTimerRef.current = window.setTimeout(() => {
      setDebounced(input);
    }, delay);
  }, [delay, input]);

  return debounced;
};

export const MainView = () => {
  const items = useSelector(selectItems);
  const [gridDisplayedRows, setGridDisplayedRows] = useState([]);
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [textFilter, setTextFilter] = useState('');
  const [engravedFilter, setEngraved] = useState('any');
  const [shippedFilter, setShippedFilter] = useState('any');
  const [readyFilter, setReadyFilter] = useState('ready');
  const [canceledFilter, setCanceledFilter] = useState('any');
  const [printedFilter, setPrintedFilter] = useState('notPrinted');
  const [selectedRows, setSelectedRows] = useState([]);
  const [gridApis, setGridApis] = useState(null);

  const debouncedTextFilter = useDebounce(textFilter, 500, '');

  const handleRescan = () => {
    scanForFiles();
  };

  const singleOrderId = (() => {
    const orderId = textFilter.trim().toUpperCase();
    return orderId.match(/^(E\d{10}|KT\d{6})$/) ? orderId : null;
  })();

  const handleFetchOrder = () => {
    getOrderItems({ orderId: singleOrderId })
      .then(handleRescan)
      .catch((err) => console.error(err));
  };

  const debouncedFromDate = useDebounce(fromDate, 500, '');
  const debouncedToDate = useDebounce(toDate, 500, '');

  useEffect(() => {
    // If there is no valid date range, don't fetch
    if (!debouncedFromDate || !debouncedToDate) return;

    // Check the date difference and avoid fetching if it's more than 3 days
    const dateDiff = getDateDifferenceInDays(
      debouncedFromDate,
      debouncedToDate
    );
    if (dateDiff > 5) {
      console.log('Date range is greater than 5 days, not fetching data');
      return;
    }

    // Fetch items if valid date range
    getItems({
      fromDate: debouncedFromDate,
      toDate: debouncedToDate,
      onlyOpen: shippedFilter === 'notShipped',
      canceled: canceledFilter,
    })
      .then(handleRescan)
      .catch((err) => console.error(err));
  }, [debouncedFromDate, debouncedToDate, shippedFilter, canceledFilter]);

  const rows = useMemo(() => {
    if (singleOrderId) return filter(items, { orderId: singleOrderId });

    if (!debouncedFromDate || !debouncedToDate) return [];

    return items
      .filter(
        (item) =>
          item.createdTimeLocal >= debouncedFromDate &&
          (item.createdTimeLocal <= debouncedToDate ||
            item.createdTimeLocal.slice(0, 10) === debouncedToDate)
      )
      .filter((item) => {
        // Apply canceled filter (if canceledFilter is 'notCancelled', exclude canceled items)
        switch (canceledFilter) {
          case 'canceled':
            return item.isCancelled;
          case 'notCanceled':
            return !item.isCancelled;
          default:
            return true; // 'any', include both canceled and not canceled items
        }
      })
      .filter((item) => !item.isCancelled)
      .filter((item) => {
        switch (shippedFilter) {
          case 'shipped':
            return item.isShipped;
          case 'notShipped':
            return !item.isShipped;
          default:
            return true;
        }
      })
      .filter((item) => {
        switch (engravedFilter) {
          case 'engraved':
            return item.isEngraved;
          case 'notEngraved':
            return !item.isEngraved;
          default:
            return true;
        }
      })
      .filter((item) => {
        switch (readyFilter) {
          case 'ready':
            return item.isReady;
          case 'notReady':
            return !item.isReady;
          default:
            return true;
        }
      })
      .filter((item) => {
        switch (printedFilter) {
          case 'printed':
            return item.isPrinted;
          case 'notPrinted':
            return !item.isPrinted;
          default:
            return true;
        }
      });
  }, [
    items,
    debouncedFromDate,
    debouncedToDate,
    shippedFilter,
    engravedFilter,
    readyFilter,
    printedFilter,
    singleOrderId,
    canceledFilter,
  ]);

  const displayedSideCount = gridDisplayedRows.reduce((acc, row) => {
    return (
      acc +
      (row.inside.text || row.inside.design ? 1 : 0) +
      (row.outside.text || row.outside.design ? 1 : 0)
    );
  }, 0);
  const displayedItemCount = gridDisplayedRows.length;
  const displayedOrderCount = uniqBy(gridDisplayedRows, 'orderId').length;

  return (
    <View>
      <div className="MainView">
        <div className="MainView__controls">
          <div>
            From <br />
            <DateInput value={fromDate} onChange={setFromDate} />
          </div>
          <div>
            To <br />
            <DateInput value={toDate} onChange={setToDate} />
          </div>
          <div>
            Find <br />
            <TextInput value={textFilter} onChange={setTextFilter} size="20" />
            <Button
              onClick={handleFetchOrder}
              style={{
                marginLeft: '10px',
                marginRight: '10px',
                fontSize: '14px',
              }}
              label="Retrieve"
              disabled={!singleOrderId}
            />
            <Button
              style={{
                fontSize: '14px',
                marginRight: '10px',
              }}
              onClick={() => setTextFilter('')}
              label="Clear"
              disabled={textFilter.length === 0}
            />
            <Button
              style={{
                fontSize: '14px',
              }}
              onClick={() => {
                getItems({
                  fromDate,
                  toDate,
                  onlyOpen: shippedFilter === 'notShipped',
                })
                  .then(handleRescan)
                  .catch((err) => console.error(err));
              }}
              label="Refresh"
            />
          </div>
          <div>
            Shipped <br />
            <SelectInput
              value={shippedFilter}
              onChange={setShippedFilter}
              options={[
                {
                  value: 'any',
                  label: 'Any',
                },
                {
                  value: 'notShipped',
                  label: 'Not Shipped',
                },
                {
                  value: 'shipped',
                  label: 'Shipped',
                },
              ]}
            />
          </div>
          {/* <div>
            Canceled <br />
            <SelectInput
              value={canceledFilter}
              onChange={setCanceledFilter}
              options={[
                {
                  value: 'any',
                  label: 'Any',
                },
                {
                  value: 'notCanceled',
                  label: 'Not Canceled',
                },
                {
                  value: 'canceled',
                  label: 'Canceled',
                },
              ]}
            />
          </div> */}
          <div>
            Type <br />
            <SelectInput
              value={engravedFilter}
              onChange={setEngraved}
              options={[
                {
                  value: 'any',
                  label: 'Any',
                },
                {
                  value: 'notEngraved',
                  label: 'No Engraving',
                },
                {
                  value: 'engraved',
                  label: 'Engrave Item',
                },
              ]}
            />
          </div>
          <div>
            Ready <br />
            <SelectInput
              value={readyFilter}
              onChange={setReadyFilter}
              options={[
                {
                  value: 'any',
                  label: 'Any',
                },
                {
                  value: 'notReady',
                  label: 'Not Ready',
                },
                {
                  value: 'ready',
                  label: 'Ready',
                },
              ]}
            />
          </div>
          <div>
            Printed <br />
            <SelectInput
              value={printedFilter}
              onChange={setPrintedFilter}
              options={[
                {
                  value: 'any',
                  label: 'Any',
                },
                {
                  value: 'notPrinted',
                  label: 'Not Printed',
                },
                {
                  value: 'printed',
                  label: 'Printed',
                },
              ]}
            />
          </div>
          <span className="spacer" />
          <div>
            <strong>{displayedSideCount}</strong> side
            {displayedItemCount === 1 ? '' : 's'} in{' '}
            <strong>{displayedItemCount}</strong> item
            {displayedItemCount === 1 ? '' : 's'} in{' '}
            <strong>{displayedOrderCount}</strong> order
            {displayedOrderCount === 1 ? '' : 's'}
          </div>
        </div>
        <Items
          rows={rows}
          gridApis={gridApis}
          setGridApis={setGridApis}
          onSelectedRowsChanged={setSelectedRows}
          onDisplayedRowsChanged={setGridDisplayedRows}
          searchText={debouncedTextFilter}
        />
      </div>
    </View>
  );
};
