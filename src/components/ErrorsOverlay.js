import React from 'react';
import { useSelector } from 'react-redux';
import { reject } from 'lodash';

import { selectErrors } from '../data/selectors';
import { dismissError } from '../data/actions';

import './ErrorsOverlay.global.scss';

export const ErrorsOverlay = () => {
  const errors = useSelector(selectErrors);

  return (
    <div className="ErrorsOverlay">
      {reject(errors, 'dismissed').map(({ id, message }) => (
        <div
          key={id}
          className="ErrorsOverlay__message"
          onClick={() => dismissError(id)}
        >
          {message}
        </div>
      ))}
    </div>
  );
};
