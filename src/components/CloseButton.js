/* eslint-disable jsx-a11y/click-events-have-key-events */
import React from 'react';
import { useHistory } from 'react-router';

import { Button } from './Button';
import closeIcon from '../../assets/close-icon.svg';

import './CloseButton.global.scss';

export const CloseButton = () => {
  const history = useHistory();
  return (
    <img
      src={closeIcon}
      className="CloseButton"
      onClick={() => history.goBack()}
    />
  );
};
