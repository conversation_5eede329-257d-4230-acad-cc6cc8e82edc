import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import { selectDesignSpecByCode } from '../data/selectors';
import { SvgPreview } from './SvgPreview';
import {
  generateSvgText,
  generateSvgDesign,
  generateSvgGuide,
} from '../lib/generateSvg';

const engravingLengthChangeTolerance = 0.01;
const engravingMaxLengthTolerance = 0.01;

const engravingScaleXBySide = {
  outside: 1.0,
  inside: 1.166,
};

export const EngravingPreview = ({
  side,
  surface,
  engraving,
  setEngraving,
}) => {
  const { designWidth, textWidth, surfaceWidth, surfaceLength } = surface;
  const {
    text,
    fontFamily,
    design,
    scale,
    _isOversize: currentIsOversize = false,
  } = engraving;

  const designSpec = useSelector((state) =>
    selectDesignSpecByCode(state, design)
  );

  const [engravingLength, setEngravingLength] = useState(0);

  const engravingContent = [];
  const otherContent = [];

  const hasUiErrors = text && design;

  if (hasUiErrors) {
    //
  } else {
    // add surface outline
    otherContent.push(
      // surface guide
      generateSvgGuide({
        length: surfaceLength,
        width: surfaceWidth,
        strokeWidth: 0.2,
        fillColor: 'white',
      })
    );

    if (text) {
      // add text content
      engravingContent.push(
        generateSvgText({
          text,
          fontFamily,
          maxWidth: textWidth,
          translateX: -engravingLength / 2,
          scale,
        }).replace(/\s+/g, ' ')
      );
      otherContent.push(
        // "max" guide
        generateSvgGuide({
          length: surfaceLength,
          width: textWidth,
          strokeWidth: 0.1,
          fillColor: '#ccffff',
        })
      );
    } else if (design) {
      // add design content
      engravingContent.push(
        generateSvgDesign({
          design,
          designSpec,
          maxWidth: designWidth,
          surfaceLength,
          surfaceWidth,
        })
      );
    }
  }

  // useEffect(() => {
  //   setEngraving((value) => ({
  //     ...value,
  //     _svg,
  //   }));
  // }, [_svg]);

  // length management
  const maxLength = surfaceLength; // TODO: allow for logo
  const handleEngravingLengthChange = (detectedLength) => {
    if (
      detectedLength > engravingLength + engravingLengthChangeTolerance ||
      detectedLength < engravingLength - engravingLengthChangeTolerance
    ) {
      setEngravingLength(detectedLength);
    }

    const scaledDetectedLength = detectedLength * engravingScaleXBySide[side]; // necessary as getBBox does not factor in scaling
    const isOversize =
      scaledDetectedLength > maxLength + engravingMaxLengthTolerance;
    if (isOversize !== currentIsOversize) {
      setEngraving((value) => ({ ...value, _isOversize: isOversize }));
    }
  };

  return (
    <div>
      <SvgPreview
        otherContent={otherContent}
        engravingContent={engravingContent}
        engravingScaleX={engravingScaleXBySide[side]}
        onEngravingLengthChange={handleEngravingLengthChange}
        surfaceLength={surfaceLength}
        surfaceWidth={surfaceWidth}
        onPrintReady={(_svg) =>
          setEngraving((value) => ({
            ...value,
            _svg,
          }))
        }
      />
    </div>
  );
};
