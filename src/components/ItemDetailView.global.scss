.ItemDetailView {
  display: flex;
  flex-direction: column;
  padding: 0 1em;
}

.ItemDetailView__header {
  display: flex;
  align-items: center;
}

.ItemDetailView__detail {
  margin-bottom: 1em;
  display: flex;
}

.ItemDetailView__additional-buttons {
  display: flex;
  justify-content: flex-end; /* Align to the right */
  margin-top: 10px; /* Optional: space between the main buttons and these new ones */
}

.ItemDetailView__detailFields {
  flex: 1;
}

.ItemDetailView__detailNotes {
  flex: 1;

  textarea {
    width: 90%;
  }

  span {
    display: inline-block;
    margin-right: 0.75em;
    text-decoration: underline;
    cursor: pointer;
  }
}

.ItemDetailView__code {
  font-size: 62.5%;
  overflow-y: scroll;
}

.ItemDetailView__previewTitle {
  display: flex;
  align-items: center;
}

.ItemDetailView__button {
  margin: 1em 1em 0 0;
}

.ItemDetailView__ringOptionSelect {
  font-size: 1.17em;
}

.ItemDetailView__problems {
  color: red;
  min-height: 2em;
  margin-top: 0.5em;
}

.ItemDetailView__infoField {
  margin: 0.2em 0;
  display: inline-block; /* This prevents the whole screen from being affected */
}

.ItemDetailView__highlight {
  color: black;
  background-color: yellow;
  border-radius: 4px; /* Adds rounded corners to the highlighted area */
  padding: 0.2em; /* Adds some padding for a better highlight effect */
}

.ItemDetailView__ringSpecControls {
  display: flex;
}

.ItemDetailView__badge {
  padding: 0.5em;
}

.ItemDetailView__badge--isReady {
  background-color: #008800;
}

.ItemDetailView__badge--notIsReady {
  color: #000000aa;
  background-color: #ccffcc;
}

.ItemDetailView__badge--isPrinted {
  background-color: #2b40ff;
}

.ItemDetailView__badge--notIsPrinted {
  color: #000000aa;
  background-color: #99ccff;
}

.ItemDetailView__openAiButton {
  margin: 1em 0;
  height: 3em;
  padding: 0 1em;
}

.ItemDetailView__copyValuesContainer {
  display: flex;
  margin-top: 10px;
  gap: 10px;
}

/* OOS Button Styles */
.ItemDetailView__oos {
  color: #fafafa;
  background-color: #d80711;
  transition: background-color 0.3s, transform 0.1s; /* Smooth transition */
}

.ItemDetailView__oos:hover {
  background-color: #c7060f; /* Slightly darker red on hover */
  cursor: pointer; /* Change cursor to pointer on hover */
}

.ItemDetailView__oos:active {
  background-color: #b2060e; /* Even darker red on active/pressed */
  transform: scale(0.98); /* Slightly shrink the button on press */
}

/* Copy Values Button Styles */
.ItemDetailView__copyValues {
  color: #fafafa;
  background-color: #2b40ff;
  transition: background-color 0.3s, transform 0.1s; /* Smooth transition */
}

.ItemDetailView__copyValues:hover {
  background-color: #2438e6; /* Slightly darker blue on hover */
  cursor: pointer; /* Change cursor to pointer on hover */
}

.ItemDetailView__copyValues:active {
  background-color: #1d2fbf; /* Even darker blue on active/pressed */
  transform: scale(0.98); /* Slightly shrink the button on press */
}

/* Copy Values Button Styles */
.ItemDetailView__printButton {
  color: #1a1919;
  background-color: #e2e2e4;
  transition: background-color 0.3s, transform 0.1s; /* Smooth transition */
}

.ItemDetailView__printButton:hover {
  background-color: #acadb6; /* Slightly darker blue on hover */
  cursor: pointer; /* Change cursor to pointer on hover */
}

.ItemDetailView__printButton:active {
  background-color: #dcdcdf; /* Even darker blue on active/pressed */
  transform: scale(0.98); /* Slightly shrink the button on press */
}
