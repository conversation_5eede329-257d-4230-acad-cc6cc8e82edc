// import fs from 'fs';
// import { getSettings } from '../data/settings';

// export const generateFile = (specs) => {
//   const {
//     fileName, // full file local target path
//     quantity, // quantity (number or string)
//     // style, // KT style code (anycase string)
//     // size, // standard size value (number or string)
//     // width, // width in mm (number or string)
//     // color, // KT color code (anycase string)
//     insideSvg = '', // SVG content (string)
//     outsideSvg = '', // SVG content (string)
//     hint1 = '', // first line of hint (large text), e.g. style and size
//     hint2 = '', // second line of hint, e.g. pseudo-filename
//   } = specs;

//   const { configBasePath, filesBasePath } = getSettings();

//   // GET TEMPLATE, LOAD CONTENT
//   const templateFilePath = `${configBasePath}template.svg`;
//   let content = fs.readFileSync(templateFilePath, 'utf-8');

//   // ADD HINTS, QUANTITY
//   content = content
//     .replace(/>(SCF-S10|\${hint1})</, `>${hint1}<`)
//     .replace(/>(SCF-S10-Silver-KT33068-TanyaH|\${hint2})</, `>${hint2}<`)
//     .replace(/>(Qty: 1|\${qty})</, `>Qty: ${quantity}<`)
//     .replace(
//       '</svg>',
//       [
//         `<g id="inside" transform="translate(232.425 143.025) rotate(90)">${insideSvg}</g>`,
//         `<g id="outside" transform="translate(300 143.025) rotate(90)">${outsideSvg}</g>`,
//         '</svg>',
//       ].join('')
//     );

//   const outputFilePath = `${filesBasePath}${fileName}`;

//   // WRITE OUTPUT
//   fs.writeFileSync(outputFilePath, content, (err) => {
//     if (err) throw err;
//   });

//   console.log('Wrote to', outputFilePath);
// };
