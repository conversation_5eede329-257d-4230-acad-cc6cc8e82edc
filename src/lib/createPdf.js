import { ipc<PERSON><PERSON><PERSON> } from 'electron';

import { getSettings } from '../data/settings';

export const createPdf = async ({
  svg,
  surfaceLength,
  surfaceWidth,
  pdfSurfaceLength,
  fileName,
  side,
  rotate = true,
  openPdf = true,
  power: fillColor,
}) => {
  const { configBasePath, outputBasePath } = getSettings();

  try {
    const result = await ipcRenderer.invoke('create-pdf', {
      svg,
      surfaceLength,
      surfaceWidth,
      pdfSurfaceLength,
      fileName,
      side,
      rotate,
      openPdf,
      power: fillColor,
      configBasePath,
      outputBasePath,
    });

    if (!result.success) {
      const message = result.error || 'Failed to create PDF';
      // Show a visible error dialog in the renderer
      try {
        ipcRenderer.send('show-error-dialog', 'Print failed', message);
      } catch (_) {
        // ignore
      }
      throw new Error(message);
    }

    return result.outputPath;
  } catch (error) {
    console.error('Error creating PDF:', error);
    try {
      ipcRenderer.send(
        'show-error-dialog',
        'Print failed',
        error?.message || String(error)
      );
    } catch (_) {
      // ignore
    }
    throw error;
  }
};
