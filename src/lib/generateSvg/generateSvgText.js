import { encode } from 'html-entities';

const normalizeText = (text) => {
  let normalized = text.trim();
  normalized = normalized.replace(/♡|♥|💙|💚|💛|🧡|💜|🖤|<heart>/g, '❤');
  normalized = normalized.replace(/♾|<infinity>/g, '∞');
  normalized = normalized.replace(/<bullet>/g, '•');
  normalized = normalized.replace(/・/g, ' • '); // this needs extra spaces around (original character is spaced)
  normalized = normalized.replace(/<degree>/g, '°');
  return normalized;
};

export const generateSvgText = ({
  text: rawText,
  maxWidth: targetWidth,
  translateX = 0,
  fontFamily: baseFontFamily = 'Georgia;Bold*',
  scale,
}) => {
  if (!rawText) return '';

  const tspans = [];
  let scaleX = scale || 1; // fonts may modify
  const scaleY = scale || 1;

  // text normalization
  const text = normalizeText(rawText);

  // text content detection

  const hasUppercase = !!text.match(/[A-Z]/);
  const hasLowercase = !!text.match(/[a-z]/);
  const hasDigits = !!text.match(/[0-9]/);
  const isRomanNumerals =
    !!text.match(/[CILMVX]/) && !text.match(/[ABD-HJKN-UWYZ0-9a-z]/);

  // font-specific transforms

  switch (baseFontFamily) {
    case 'Georgia;Bold*':
    case 'Georgia;Bold':
    case 'Georgia;BoldItalic': {
      const hasDescenders = !!text.match(/[fgjpqy@$()[\]{}\\|/]/);

      // font
      let fontFamily = baseFontFamily;
      if (baseFontFamily === 'Georgia;Bold*')
        fontFamily = isRomanNumerals ? 'Georgia;Bold' : 'Georgia;BoldItalic';

      // size and scale
      let y = 0;
      const baseFontSize = targetWidth;
      const fontSize = baseFontSize;
      scaleX *= 1.2; // thicken Georgia

      if (!hasDescenders && hasUppercase) y = targetWidth * 0.1;

      tspans.push(
        ...text.split('').map((character) => ({
          text: character,
          fontFamily,
          fontSize,
          y,
          letterSpacing: 0,
        }))
      );
      break;
    }
    default:
      throw new Error('Unknown font', baseFontFamily);
  }

  // symbol replacements

  tspans.forEach((character) => {
    if (character.text === '❤') {
      character.fontFamily = 'MS-PGothic, MS PGothic';
      character.fontSize *= 1.4;
      character.y = targetWidth * 0.2;
      character.spaceBefore = 1;
      character.spaceAfter = 1;
    }
    if (character.text === '∞') {
      character.fontSize *= 1.5;
      character.y = targetWidth * 0.05;
    }
  });

  // string build

  const tspanContent = tspans
    .map(
      (
        {
          fontFamily: spanFontFamily,
          fontSize: spanFontSize,
          text: spanText,
          spaceAfter = 0,
          y = 0,
        },
        index
      ) => {
        const letterSpacing = (() => {
          const nextTspan = tspans[index + 1];
          if (!nextTspan) return 0; // no next character, therefore no letter spacing
          const { spaceBefore = 0 } = nextTspan;
          return Math.max(spaceAfter, spaceBefore); // larger of this spaceAfter or next's spaceBefore
        })();

        const [cssFontFamily, fontModifiers = ''] = spanFontFamily.split(';');

        const spanFontWeight = fontModifiers.includes('Bold')
          ? 'bold'
          : 'normal';

        const spanFontStyle = fontModifiers.includes('Italic')
          ? 'italic'
          : 'normal';

        // const cssFontFamily =

        return `<tspan
          alignment-baseline="middle"
          style="
            font-family: ${cssFontFamily};
            font-size: ${spanFontSize}px;
            font-weight: ${spanFontWeight};
            font-style: ${spanFontStyle};
            letter-spacing: ${letterSpacing};
          "
          y="${y}">${encode(spanText)}</tspan>`; // do not pretty-format, leading and trailing spaces will be included
      }
    )
    .join('');

  return `
    <text
      class="svg__text"
      style="
        stroke: none;
      "
      transform="translate(${translateX}) scale(${scaleX} ${scaleY})"
    >${tspanContent}</text>
  `;
};
