import fs from 'fs';
import path from 'path';
import { times } from 'lodash';

import { getSettings } from '../../data/settings';

const readAndScrubSvgFile = (filePath) => {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf-8');
    const svgTag = fileContent.match(/<svg[^>]*>/)[0];
    const scrubbedSvgTag = svgTag
      .replace(/ x=".+?"/, '')
      .replace(/ y=".+?"/, '')
      .replace(/ width=".+?"/, '')
      .replace(/ height=".+?"/, '')
      .replace(/ enable-background=".+?"/, '');
    return fileContent.replace(svgTag, scrubbedSvgTag);
  } catch (error) {
    return null;
  }
};

const getSvgAspectRatio = (designSvg) => {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [wholeMatch, width, height] =
    designSvg.match(/viewBox="[\d.]+ [\d.]+ ([\d.]+) ([\d.]+)"/) || [];
  if (!wholeMatch) throw new Error('Error parsing SVG file viewBox.');
  return Number(width) / Number(height);
};

const placedSvg = (svg, { x, y, width, height }) => {
  return svg.replace(
    /<svg ([^>]*?)>/,
    `<svg $1
      preserveAspectRatio="none"
      x="${x}"
      y="${y}"
      width="${width}"
      height="${height}"
    >`
  );
};

const errorText = (text) =>
  `<text style="font-size: 8px" y="12">${text}</text>`;

export const generateSvgDesign = ({
  design,
  designSpec,
  maxWidth: targetWidth,
  surfaceLength,
  surfaceWidth,
}) => {
  const { configBasePath } = getSettings();
  const designsBasePath = path.join(configBasePath, 'designs');

  const innerContent = [];
  if (!designSpec)
    innerContent.push(errorText(`Design spec not found for "${design}"`));

  if (designSpec) {
    const { strategy } = designSpec;
    switch (strategy) {
      case 'repeat-preserve-aspect':
      case 'repeat-condense':
      case 'repeat-stretch':
      case 'repeat-flex': {
        const designSvg = readAndScrubSvgFile(
          path.join(designsBasePath, `${design.trim()}.svg`)
        );
        if (!designSvg) {
          innerContent.push(errorText(`File not found "${design.trim()}.svg"`));
          break;
        }

        const aspectRatio = getSvgAspectRatio(designSvg);

        const fractionalRepeats = surfaceLength / (targetWidth * aspectRatio);

        const wholeRepeats = (() => {
          switch (strategy) {
            case 'repeat-stretch':
              return Math.floor(fractionalRepeats);
            case 'repeat-flex':
              return Math.round(fractionalRepeats);
            default:
              return Math.ceil(fractionalRepeats);
          }
        })();

        const [scaleX, scaleY] = (() => {
          const scale = fractionalRepeats / wholeRepeats;
          switch (strategy) {
            case 'repeat-preserve-aspect':
              return [scale, scale];
            default:
              return [scale, 1];
          }
        })();

        const width = targetWidth * scaleX * aspectRatio;
        const height = targetWidth * scaleY;
        const y = (surfaceWidth - height) / 2;

        times(wholeRepeats, (index) => {
          innerContent.push(
            placedSvg(designSvg, {
              width,
              height,
              x: index * width,
              y,
            })
          );
        });
        break;
      }
      case 'fill': {
        const designSvg = readAndScrubSvgFile(
          path.join(designsBasePath, `${design}.svg`)
        );
        if (!designSvg) {
          innerContent.push(errorText(`File not found "${design}.svg"`));
          break;
        }

        const height = targetWidth;

        innerContent.push(
          placedSvg(designSvg, {
            width: surfaceLength,
            height: targetWidth,
            x: 0,
            y: (surfaceWidth - height) / 2,
          })
        );

        break;
      }
      case 'center-preserve-aspect': {
        const designSvg = readAndScrubSvgFile(
          path.join(designsBasePath, `${design}.svg`)
        );
        if (!designSvg) {
          innerContent.push(errorText(`File not found "${design}.svg"`));
          break;
        }

        const aspectRatio = getSvgAspectRatio(designSvg);

        let height = targetWidth;
        let width = targetWidth * aspectRatio;

        if (width > surfaceLength) {
          height /= width / surfaceLength;
          width = surfaceLength;
        }

        innerContent.push(
          placedSvg(designSvg, {
            width,
            height,
            x: surfaceLength / 2 - width / 2,
            y: (surfaceWidth - height) / 2,
          })
        );

        break;
      }
      case 'repeat-AB-spacing': {
        const designSvgA = readAndScrubSvgFile(
          path.join(designsBasePath, `${design}-A.svg`)
        );
        if (!designSvgA) {
          innerContent.push(errorText(`File not found "${design}-A.svg"`));
          break;
        }
        const designSvgB = readAndScrubSvgFile(
          path.join(designsBasePath, `${design}-B.svg`)
        );
        if (!designSvgB) {
          innerContent.push(errorText(`File not found "${design}-B.svg"`));
          break;
        }

        const { minSpacingFactor, minRepeats } = designSpec;
        const minSpacing = minSpacingFactor * targetWidth;

        const aspectRatioA = getSvgAspectRatio(designSvgA);
        const aspectRatioB = getSvgAspectRatio(designSvgB);

        const widthA = targetWidth * aspectRatioA;
        const widthB = targetWidth * aspectRatioB;
        const widthCombined = widthA + widthB + minSpacing * 2;

        const fractionalRepeats = surfaceLength / widthCombined;
        let wholeRepeats = Math.floor(fractionalRepeats);
        let scale = 1;

        if (minRepeats && wholeRepeats < minRepeats) {
          wholeRepeats = minRepeats;
          scale = fractionalRepeats / wholeRepeats;
        }

        const scaledWidthA = widthA * scale;
        const scaledWidthB = widthB * scale;
        const scaledHeight = targetWidth * scale;

        const scaledSpacing =
          (surfaceLength - wholeRepeats * (scaledWidthA + scaledWidthB)) /
          wholeRepeats /
          2;

        const y = (surfaceWidth - scaledHeight) / 2;

        times(wholeRepeats, (index) => {
          innerContent.push(
            placedSvg(designSvgA, {
              width: scaledWidthA,
              height: scaledHeight,
              x: index * (scaledWidthA + scaledWidthB + scaledSpacing * 2),
              y,
            })
          );
          innerContent.push(
            placedSvg(designSvgB, {
              width: scaledWidthB,
              height: scaledHeight,
              x:
                index * (scaledWidthA + scaledWidthB + scaledSpacing * 2) +
                scaledWidthA +
                scaledSpacing,
              y,
            })
          );
        });

        break;
      }
      case 'repeat-spacing': {
        const designSvg = readAndScrubSvgFile(
          path.join(designsBasePath, `${design}.svg`)
        );
        if (!designSvg) {
          innerContent.push(errorText(`File not found "${design}.svg"`));
          break;
        }

        const minSpacing = designSpec.minSpacingFactor * targetWidth;
        const aspectRatio = getSvgAspectRatio(designSvg);
        const width = targetWidth * aspectRatio;

        const fractionalRepeats = surfaceLength / (width + minSpacing);
        const wholeRepeats = Math.floor(fractionalRepeats);
        const spacing = (surfaceLength - wholeRepeats * width) / wholeRepeats;

        const height = targetWidth;
        const y = (surfaceWidth - height) / 2;

        times(wholeRepeats, (index) => {
          innerContent.push(
            placedSvg(designSvg, {
              width,
              height,
              x: index * (width + spacing),
              y,
            })
          );
        });

        break;
      }
      default:
        innerContent.push(errorText(`Invalid strategy "${strategy}"`));
    }
  }

  return `
    <svg version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      viewBox="0 0 ${surfaceLength} ${surfaceWidth}"
      x="${-surfaceLength / 2}"
      y="${-surfaceWidth / 2}"
      width="${surfaceLength}"
      height="${surfaceWidth}"
    ><g class="svg__design">${innerContent.join('')}</g></svg>`;
};
