import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Switch, Route } from 'react-router-dom';
import { Provider } from 'react-redux';

import { store } from './data/store';
import { Settings } from './components/Settings';
import { Header } from './components/Header';
import { ItemDetailView } from './components/ItemDetailView';
import { MainView } from './components/MainView';
import { ErrorsOverlay } from './components/ErrorsOverlay';
import { getSettings } from './data/settings';
import { loadConfigFromCloud, loadLocalDataFromFile } from './data/actions';

import './App.global.css';

export default function App() {
  const { configBasePath } = getSettings();

  const [isReady, setIsReady] = useState(false);

  useEffect(() => {
    (async () => {
      // await loadConfigFromLocal();
      await loadConfigFromCloud();
      loadLocalDataFromFile();
      setIsReady(true);
    })();
  }, [configBasePath]);

  // if (!configBasePath) return <Settings />;
  if (!isReady) return <div>loading...</div>;

  return (
    <Provider store={store}>
      <Router>
        <ErrorsOverlay />
        <Route path="/" component={Header} />
        <Route path="/" component={MainView} />
        <Switch>
          <Route
            path="/designer"
            render={() => (
              <ItemDetailView match={{ params: { itemId: null } }} />
            )}
          />
          <Route path="/item/:itemId" component={ItemDetailView} />
          <Route path="/settings" component={Settings} />
        </Switch>
      </Router>
    </Provider>
  );
}
