import { parseSku, extractCompleteOrderData } from '../data/skuUtil';

// Mock the store and selectors
jest.mock('../data/store', () => ({
  store: {
    getState: () => ({
      config: {
        ringSpecs: [],
        colors: [{ code: 'GOLD' }, { code: 'SILVER' }, { code: 'BLACK' }],
        designSpecs: [
          { code: 'floral' },
          { code: 'Floral' },
          { code: 'geometric' },
          { code: 'Fili' },
        ],
      },
    }),
  },
}));

jest.mock('../data/selectors', () => ({
  selectSkuValidParams: () => [
    { value: 'CF6', type: 'style' },
    { value: 'CF4', type: 'style' },
    { value: 'C4', type: 'style' },
    { value: 'D6', type: 'style' },
    { value: 'S11', type: 'size' },
    { value: 'S08', type: 'size' },
    { value: 'GOLD', type: 'color' },
    { value: 'SILVER', type: 'color' },
    { value: 'BLACK', type: 'color' },
    { value: 'floral', type: 'design' },
    { value: 'Floral', type: 'design' },
    { value: 'geometric', type: 'design' },
    { value: 'Fili', type: 'design' },
  ],
}));

describe('SKU Utility Functions', () => {
  describe('parseSku', () => {
    it('should parse basic SKU without inlay', () => {
      const result = parseSku('G-CF6-S11-floral-GOLD');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'floral',
        color: 'GOLD',
        inlayType: 'gold', // Should detect gold from color
      });
    });

    it('should detect gold inlay from gs prefix', () => {
      const result = parseSku('G-CF6-S11-gsFloral-GOLD');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'gsFloral',
        color: 'GOLD',
        inlayType: 'gold',
      });
    });

    it('should detect silver inlay from ss prefix', () => {
      const result = parseSku('G-CF6-S11-ssFloral-SILVER');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'ssFloral',
        color: 'SILVER',
        inlayType: 'silver',
      });
    });

    it('should detect clear inlay from cs prefix', () => {
      const result = parseSku('G-CF6-S11-csFloral-BLACK');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'csFloral',
        color: 'BLACK',
        inlayType: 'clear',
      });
    });

    it('should detect gold inlay from -gs- pattern', () => {
      const result = parseSku('G-CF6-S11-gsFili-GOLD');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'gsFili',
        color: 'GOLD',
        inlayType: 'gold',
      });
    });

    it('should detect silver inlay from -ss- pattern', () => {
      const result = parseSku('G-CF6-S11-ssFili-SILVER');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'ssFili',
        color: 'SILVER',
        inlayType: 'silver',
      });
    });

    it('should detect clear inlay from -cs- pattern', () => {
      const result = parseSku('G-CF6-S11-csFili-BLACK');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        design: 'csFili',
        color: 'BLACK',
        inlayType: 'clear',
      });
    });

    it('should detect clear inlay from csClad pattern', () => {
      const result = parseSku('G-CF6-S11-csClad-BLACK');
      expect(result).toEqual({
        style: 'CF6',
        size: 'S11',
        color: 'BLACK',
        inlayType: 'clear',
      });
    });

    it('should return empty object for non-G SKUs', () => {
      const result = parseSku('INVALID-SKU');
      expect(result).toEqual({});
    });
  });

  describe('extractCompleteOrderData', () => {
    it('should convert CF4 style to C4', () => {
      const result = extractCompleteOrderData({
        sku: 'ComfortFit-4mm-GreenEnchanted-12',
        source: 'test',
      });
      expect(result.style).toBe('C4');
    });

    it('should convert CF-4mm style to C4', () => {
      const result = extractCompleteOrderData({
        sku: 'CF-4mm-StarPurple-11',
        source: 'test',
      });
      expect(result.style).toBe('C4');
    });

    it('should keep CF6 style unchanged', () => {
      const result = extractCompleteOrderData({
        sku: 'ComfortFit-6mm-GreenEnchanted-12',
        source: 'test',
      });
      expect(result.style).toBe('CF6');
    });

    it('should extract inlay type from SKU', () => {
      const result = extractCompleteOrderData({
        sku: 'G-CF6-S11-gsFili-GOLD',
        source: 'test',
      });
      expect(result.inlayType).toBe('gold');
    });

    it('should prioritize existing data over SKU extraction', () => {
      const result = extractCompleteOrderData({
        style: 'D6',
        color: 'SILVER',
        sku: 'G-CF6-S11-floral-GOLD',
        source: 'test',
      });
      expect(result.style).toBe('D6');
      expect(result.color).toBe('SILVER');
    });
  });
});
