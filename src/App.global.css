/*
 * @NOTE: Prepend a `~` to css file paths that are in your node_modules
 *        See https://github.com/webpack-contrib/sass-loader#imports
 */
body {
  position: relative;
  color: white;
  /* background: linear-gradient(
    200.96deg,
    #fedc2a -29.09%,
    #dd5789 51.77%,
    #7a2c9e 129.35%
  ); */
  background: #333333;
  font-family: sans-serif;
  overflow-y: hidden;
  margin: 0;
  padding: 0;
}

.Modal {
  position: absolute;
  top: 50%;
  left: 50%;
  right: auto;
  bottom: auto;
  transform: translate(-50%, -50%);
  background: white;
  padding: 20px;
  border-radius: 8px;
  color: black;
}

.Overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.75);
}

li {
  list-style: none;
}

a {
  text-decoration: none;
  height: fit-content;
  width: fit-content;
  margin: 10px;
  color: unset;
}

a:hover {
  opacity: 1;
  text-decoration: none;
}

svg .svg__text {
  fill: #000000;
  white-space: pre;
}

@import '~ag-grid-community/dist/styles/ag-grid.css';
@import '~ag-grid-community/dist/styles/ag-theme-balham.css';
@import '~ag-grid-community/dist/styles/ag-theme-balham-dark.css';

.spacer {
  flex: 1;
}

textarea {
  font-family: sans-serif;
}

h3 {
  margin: 0.5em 0;
}

button,
input,
textarea {
  font-size: 1rem;
}
