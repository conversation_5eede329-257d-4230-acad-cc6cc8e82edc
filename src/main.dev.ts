/* eslint global-require: off, no-console: off */

/**
 * This module executes inside of electron's main process. You can start
 * electron renderer process from here and communicate with the other processes
 * through IPC.
 *
 * When running `yarn build` or `yarn build:main`, this file is compiled to
 * `./src/main.prod.js` using webpack. This gives us some performance wins.
 */
import 'core-js/stable';
import 'regenerator-runtime/runtime';
import path from 'path';
import fs from 'fs';
import { app, BrowserWindow, shell, ipcMain, dialog } from 'electron';
import MenuBuilder from './menu';

// Load environment variables
import dotenv from 'dotenv';
dotenv.config();

// Import PDF creation dependencies
import SVGtoPDF from 'svg-to-pdfkit';
import PDFDocument from 'pdfkit';

let mainWindow: BrowserWindow | null = null;

if (process.env.NODE_ENV === 'production') {
  const sourceMapSupport = require('source-map-support');
  sourceMapSupport.install();
}

const installExtensions = async () => {
  const installer = require('electron-devtools-installer');
  const forceDownload = !!process.env.UPGRADE_EXTENSIONS;
  const extensions = ['REACT_DEVELOPER_TOOLS', 'REDUX_DEVTOOLS'];

  return installer
    .default(
      extensions.map((name) => installer[name]),
      forceDownload
    )
    .catch(console.log);
};

const createWindow = async () => {
  if (
    process.env.NODE_ENV === 'development' ||
    process.env.DEBUG_PROD === 'true'
  ) {
    await installExtensions();
  }

  const RESOURCES_PATH = app.isPackaged
    ? path.join(process.resourcesPath, 'assets')
    : path.join(__dirname, '../assets');

  const getAssetPath = (...paths: string[]): string => {
    return path.join(RESOURCES_PATH, ...paths);
  };

  mainWindow = new BrowserWindow({
    show: false,
    width: 1024,
    height: 728,
    icon: getAssetPath('icon.png'),
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false,
    },
  });

  mainWindow.loadURL(`file://${__dirname}/index.html`);

  // @TODO: Use 'ready-to-show' event
  //        https://github.com/electron/electron/blob/master/docs/api/browser-window.md#using-ready-to-show-event
  mainWindow.webContents.on('did-finish-load', () => {
    if (!mainWindow) {
      throw new Error('"mainWindow" is not defined');
    }
    if (process.env.START_MINIMIZED) {
      mainWindow.minimize();
    } else {
      mainWindow.show();
      mainWindow.focus();
    }
  });

  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  const menuBuilder = new MenuBuilder(mainWindow);
  menuBuilder.buildMenu();

  // Open urls in the user's browser
  mainWindow.webContents.on('new-window', (event, url) => {
    event.preventDefault();
    shell.openExternal(url);
  });
};

/**
 * Add event listeners...
 */

// IPC listener for showing error dialogs
ipcMain.on('show-error-dialog', (event, title, message) => {
  dialog.showErrorBox(title, message);
});

// Resolve path to bundled assets (works in dev and production)
const RESOURCES_PATH = app.isPackaged
  ? path.join(process.resourcesPath, 'assets')
  : path.join(__dirname, '../assets');

// IPC handler for creating PDF
ipcMain.handle(
  'create-pdf',
  async (
    event,
    {
      svg,
      surfaceLength,
      surfaceWidth,
      pdfSurfaceLength,
      fileName,
      side,
      rotate = true,
      openPdf = true,
      power: fillColor,
      configBasePath,
      outputBasePath,
    }
  ) => {
    try {
      const fontNames = ['Georgia-Bold', 'Georgia-BoldItalic', 'MS-PGothic'];

      const outputSurfaceLength = pdfSurfaceLength || surfaceLength;

      const dimensions = rotate
        ? [surfaceWidth, outputSurfaceLength]
        : [outputSurfaceLength, surfaceWidth];

      const doc = new PDFDocument({
        layout: 'portrait',
        size: dimensions,
        margins: {
          top: 0,
          bottom: 0,
          left: 0,
          right: 0,
        },
      });

      // Resolve fonts directory: prefer user-configured, otherwise bundled assets
      const fontsDirCandidates = [] as string[];
      if (configBasePath) {
        fontsDirCandidates.push(path.resolve(configBasePath, 'fonts'));
      }
      fontsDirCandidates.push(path.join(RESOURCES_PATH, 'fonts'));

      // Register fonts from the first location where they exist
      fontNames.forEach((fontName) => {
        let registered = false;
        for (const dir of fontsDirCandidates) {
          const candidatePath = path.join(dir, `${fontName}.ttf`);
          if (fs.existsSync(candidatePath)) {
            const fontFile = fs.readFileSync(candidatePath);
            doc.registerFont(fontName, fontFile);
            registered = true;
            break;
          }
        }
        if (!registered) {
          throw new Error(
            `Required font not found: ${fontName}.ttf (looked in: ${fontsDirCandidates.join(
              ', '
            )})`
          );
        }
      });

      // Resolve output directory: prefer user-configured, otherwise Downloads/KTHQ
      const outputDir =
        (outputBasePath && outputBasePath.trim()) ||
        path.join(app.getPath('downloads'), 'KTHQ');
      fs.mkdirSync(outputDir, { recursive: true });
      const outputPath = path.join(outputDir, `${fileName}-${side}.pdf`);

      let gTransform = '';
      if (rotate) gTransform += ` rotate(90) translate(0 -${surfaceWidth})`;
      if (pdfSurfaceLength)
        gTransform += ` translate(${(pdfSurfaceLength - surfaceLength) / 2} 0)`;

      const scrubbedSvg = svg
        .replace(/font-family:([^,;]+?), .+?;/g, 'font-family:$1;') // TODO: use classes for fonts, don't hack the SVG
        .replaceAll(/#000000|#ff0000/gi, fillColor);

      const rotatedSvg = `<svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      xmlns:xlink="http://www.w3.org/1999/xlink"
      x="0"
      y="0"
      viewBox="0 0 ${dimensions.join(' ')}"
      >
        <style>
          .svg__text {
            fill: ${fillColor};
            white-space: pre;
          }
        </style>
        <rect
          x="0"
          y="0"
          width="${dimensions[0]}"
          height="${dimensions[1]}"
          style="
            fill: white;
          "
        />
        <g transform="${gTransform}">
          ${scrubbedSvg}
        </g>
      </svg>`;

      doc.pipe(fs.createWriteStream(outputPath));
      SVGtoPDF(doc, rotatedSvg);
      doc.end();

      if (openPdf) {
        // Open the PDF file (cross-platform)
        const { exec } = require('child_process');
        const platform = process.platform;
        const openCmd =
          platform === 'darwin'
            ? `open "${outputPath}"`
            : platform === 'win32'
            ? `start "" "${outputPath}"`
            : `xdg-open "${outputPath}"`;
        exec(openCmd);
      }

      return { success: true, outputPath };
    } catch (error) {
      console.error('Error creating PDF:', error);
      try {
        dialog.showErrorBox(
          'Print failed',
          (error as Error)?.message || String(error)
        );
      } catch (_) {
        // ignore dialog errors
      }
      return { success: false, error: error.message };
    }
  }
);

app.on('window-all-closed', () => {
  // Respect the OSX convention of having the application in memory even
  // after all windows have been closed
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.whenReady().then(createWindow).catch(console.log);

app.on('activate', () => {
  // On macOS it's common to re-create a window in the app when the
  // dock icon is clicked and there are no other windows open.
  if (mainWindow === null) createWindow();
});
