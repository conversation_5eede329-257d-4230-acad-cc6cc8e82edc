/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */

/**
 * Shopify Actions - Dual API Format Support
 *
 * This module supports both legacy and extended Shopify API response formats:
 *
 * - Legacy Format: Simple structure with basic order data
 * - Extended Format: Comprehensive structure with additional fields like:
 *   - admin_graphql_api_id, current_subtotal_price_set, client_details
 *   - Enhanced customer, billing_address, and shipping_address objects
 *   - Detailed line_items with price_set objects
 *   - shipping_lines array with detailed shipping information
 *
 * The code automatically detects which format is being used and transforms
 * accordingly. Set NODE_ENV=development to enable format detection logging.
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import moment from 'moment';
import { flatten } from 'lodash';

import { store } from './store';
import { selectGeneralConfig } from './selectors';
import { transformShopifyOrder } from './shopifyUtil';
import { OrderItem } from './types';

// Types
interface ShopifyOrderLegacy {
  id: string;
  name: string;
  email: string;
  created_at: string;
  financial_status: string;
  customer: {
    id: string;
    first_name: string;
    last_name: string;
    email: string;
  };
  shipping_address: {
    first_name: string;
    last_name: string;
    address1: string;
    address2?: string;
    city: string;
    province: string;
    country: string;
    zip: string;
  };
  line_items: Array<{
    id: string;
    title: string;
    sku: string;
    quantity: number;
    price: string;
  }>;
}

interface ShopifyOrderExtended {
  id: number;
  admin_graphql_api_id: string;
  app_id: number;
  browser_ip: string;
  buyer_accepts_marketing: boolean;
  cancel_reason: string | null;
  cancelled_at: string | null;
  cart_token: string | null;
  checkout_id: number;
  checkout_token: string;
  client_details: {
    accept_language: string;
    browser_height: number | null;
    browser_ip: string;
    browser_width: number | null;
    session_hash: string | null;
    user_agent: string;
  };
  closed_at: string | null;
  company: string | null;
  confirmation_number: string;
  confirmed: boolean;
  contact_email: string;
  created_at: string;
  currency: string;
  current_subtotal_price: string;
  current_subtotal_price_set: {
    shop_money: { amount: string; currency_code: string };
    presentment_money: { amount: string; currency_code: string };
  };
  current_total_price: string;
  current_total_price_set: {
    shop_money: { amount: string; currency_code: string };
    presentment_money: { amount: string; currency_code: string };
  };
  customer_locale: string;
  email: string;
  estimated_taxes: boolean;
  financial_status: string;
  fulfillment_status: string | null;
  name: string;
  note: string | null;
  number: number;
  order_number: number;
  phone: string | null;
  processed_at: string;
  source_name: string;
  subtotal_price: string;
  tags: string;
  tax_exempt: boolean;
  taxes_included: boolean;
  test: boolean;
  token: string;
  total_discounts: string;
  total_line_items_price: string;
  total_outstanding: string;
  total_price: string;
  total_tax: string;
  total_weight: number;
  updated_at: string;
  billing_address: {
    first_name: string;
    last_name: string;
    company: string | null;
    address1: string;
    address2: string | null;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone: string | null;
    name: string;
    country_code: string;
    province_code: string;
    latitude?: number | null;
    longitude?: number | null;
  };
  customer: {
    id: number;
    email: string;
    created_at: string;
    updated_at: string;
    first_name: string;
    last_name: string;
    state: string;
    note: string | null;
    verified_email: boolean;
    multipass_identifier: string | null;
    tax_exempt: boolean;
    phone: string | null;
    email_marketing_consent: {
      state: string;
      opt_in_level: string;
      consent_updated_at: string | null;
    };
    sms_marketing_consent: any | null;
    tags: string;
    currency: string;
    tax_exemptions: any[];
    admin_graphql_api_id: string;
    default_address: {
      id: number;
      customer_id: number;
      first_name: string;
      last_name: string;
      company: string | null;
      address1: string;
      address2: string | null;
      city: string;
      province: string;
      country: string;
      zip: string;
      phone: string | null;
      name: string;
      province_code: string;
      country_code: string;
      country_name: string;
      default: boolean;
    };
  };
  discount_applications: any[];
  fulfillments: any[];
  line_items: Array<{
    id: number;
    admin_graphql_api_id: string;
    attributed_staffs: any[];
    current_quantity: number;
    fulfillable_quantity: number;
    fulfillment_service: string;
    fulfillment_status: string | null;
    gift_card: boolean;
    grams: number;
    name: string;
    price: string;
    price_set: {
      shop_money: { amount: string; currency_code: string };
      presentment_money: { amount: string; currency_code: string };
    };
    product_exists: boolean;
    product_id: number | null;
    properties: Array<{
      name: string;
      value: string;
    }>;
    quantity: number;
    requires_shipping: boolean;
    sku: string | null;
    taxable: boolean;
    title: string;
    total_discount: string;
    total_discount_set: {
      shop_money: { amount: string; currency_code: string };
      presentment_money: { amount: string; currency_code: string };
    };
    variant_id: number | null;
    variant_inventory_management: string | null;
    variant_title: string | null;
    vendor: string;
    tax_lines: any[];
    duties: any[];
    discount_allocations: any[];
  }>;
  payment_terms: any | null;
  refunds: any[];
  shipping_address: {
    first_name: string;
    last_name: string;
    company: string | null;
    address1: string;
    address2: string | null;
    city: string;
    province: string;
    country: string;
    zip: string;
    phone: string | null;
    name: string;
    country_code: string;
    province_code: string;
    latitude?: number | null;
    longitude?: number | null;
  };
  shipping_lines: Array<{
    id: number;
    carrier_identifier: string | null;
    code: string;
    discounted_price: string;
    discounted_price_set: {
      shop_money: { amount: string; currency_code: string };
      presentment_money: { amount: string; currency_code: string };
    };
    phone: string | null;
    price: string;
    price_set: {
      shop_money: { amount: string; currency_code: string };
      presentment_money: { amount: string; currency_code: string };
    };
    source: string;
    title: string;
    tax_lines: any[];
    discount_allocations: any[];
  }>;
}

// Union type for both order formats
type ShopifyOrder = ShopifyOrderLegacy | ShopifyOrderExtended;

interface ShopifyOrdersResponse {
  orders: ShopifyOrder[];
}

interface ShopifyOrderResponse {
  order: ShopifyOrder;
}

interface GetOrdersParams {
  fromDate: string;
  toDate: string;
  onlyOpen: boolean;
  canceled: boolean;
  onProgress?: (payload: OrderItem[]) => void;
}

interface GetOrderParams {
  orderId: string;
  onProgress?: (payload: OrderItem[]) => void;
}

interface CreateOrderParams {
  item: OrderItem;
}

// Type guards and detection
/**
 * Detects if an order uses the extended Shopify API format
 * Extended format includes admin_graphql_api_id, price_set objects, client_details, etc.
 */
const isExtendedOrder = (order: any): order is ShopifyOrderExtended => {
  return (
    order &&
    typeof order.admin_graphql_api_id === 'string' &&
    typeof order.current_subtotal_price_set === 'object' &&
    typeof order.client_details === 'object' &&
    Array.isArray(order.shipping_lines)
  );
};

/**
 * Detects if an order uses the legacy Shopify API format
 * Legacy format has simpler structure without extended fields
 */
const isLegacyOrder = (order: any): order is ShopifyOrderLegacy => {
  return (
    order &&
    typeof order.id === 'string' &&
    !order.admin_graphql_api_id &&
    !order.current_subtotal_price_set
  );
};

// Order transformation functions
/**
 * Transforms legacy format orders using the existing transform function
 */
const transformLegacyOrder = (order: ShopifyOrderLegacy): OrderItem[] => {
  // Check if this order should be excluded entirely based on having only "- Options" items
  const hasNonOptionsItems = order.line_items.some(
    (item) => !item.title.includes('- Options')
  );

  if (!hasNonOptionsItems) {
    if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
      console.log(
        `Skipping order ${order.name} - all line items contain "- Options"`
      );
    }
    return [];
  }

  // Filter out line items with "- Options" in title before transformation
  const filteredOrder = {
    ...order,
    line_items: order.line_items.filter(
      (item) => !item.title.includes('- Options')
    ),
  };

  return transformShopifyOrder(filteredOrder);
};

/**
 * Transforms extended format orders by converting to legacy format first
 * This ensures compatibility with existing transformation logic
 */
const transformExtendedOrder = (order: ShopifyOrderExtended): OrderItem[] => {
  // Check if this order should be excluded entirely based on having only "- Options" items
  const hasNonOptionsItems = order.line_items.some(
    (item) => !item.title.includes('- Options')
  );

  if (!hasNonOptionsItems) {
    if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
      console.log(
        `Skipping order ${order.name} - all line items contain "- Options"`
      );
    }
    return [];
  }

  // Filter out line items with "- Options" in title
  const filteredLineItems = order.line_items.filter(
    (item) => !item.title.includes('- Options')
  );

  // Convert extended format to legacy-compatible format for existing transform function
  const legacyOrder: any = {
    id: order.id.toString(),
    name: order.name,
    email: order.email,
    created_at: order.created_at,
    financial_status: order.financial_status,
    current_subtotal_price: order.current_subtotal_price,
    currency: order.currency,
    cancelled_at: order.cancelled_at,
    closed_at: order.closed_at,
    customer: {
      id: order.customer.id.toString(),
      first_name: order.customer.first_name,
      last_name: order.customer.last_name,
      email: order.customer.email,
    },
    shipping_address: {
      first_name: order.shipping_address.first_name,
      last_name: order.shipping_address.last_name,
      name: order.shipping_address.name,
      address1: order.shipping_address.address1,
      address2: order.shipping_address.address2 || '',
      city: order.shipping_address.city,
      province: order.shipping_address.province,
      country: order.shipping_address.country,
      zip: order.shipping_address.zip,
      country_code: order.shipping_address.country_code,
      province_code: order.shipping_address.province_code,
      phone: order.shipping_address.phone,
    },
    line_items: filteredLineItems.map((item) => ({
      id: item.id.toString(),
      title: item.title,
      sku: item.sku || '',
      quantity: item.quantity,
      price: item.price,
      properties: item.properties || [],
      note: item.name || '',
    })),
  };

  return transformShopifyOrder(legacyOrder);
};

/**
 * Main transformation function that auto-detects order format and transforms accordingly
 * Supports both legacy and extended Shopify API response formats
 */
const transformOrder = (order: ShopifyOrder): OrderItem[] => {
  if (isExtendedOrder(order)) {
    if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
      console.log(`Processing order ${order.name} with Extended API format`);
    }
    return transformExtendedOrder(order);
  } else if (isLegacyOrder(order)) {
    if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
      console.log(`Processing order ${order.name} with Legacy API format`);
    }
    return transformLegacyOrder(order);
  } else {
    console.warn(
      'Unknown order format detected, attempting legacy transform:',
      order
    );
    return transformShopifyOrder(order as any);
  }
};

/**
 * Utility function to analyze order format distribution in a batch
 * Useful for monitoring which API format versions are being returned
 * @param orders Array of orders to analyze
 * @returns Statistics object with counts for each format type
 */
const analyzeOrderFormats = (
  orders: ShopifyOrder[]
): {
  extended: number;
  legacy: number;
  unknown: number;
  total: number;
  filteredOut: number;
} => {
  const stats = {
    extended: 0,
    legacy: 0,
    unknown: 0,
    total: orders.length,
    filteredOut: 0,
  };

  orders.forEach((order) => {
    // Check if order has any line items that don't contain "- Options"
    const hasValidLineItems = order.line_items.some(
      (item) => !item.title.includes('- Options')
    );

    if (!hasValidLineItems) {
      stats.filteredOut++;
      if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
        console.log(
          `Order ${order.name} will be filtered out - all line items contain "- Options"`
        );
      }
    }

    if (isExtendedOrder(order)) {
      stats.extended++;
    } else if (isLegacyOrder(order)) {
      stats.legacy++;
    } else {
      stats.unknown++;
    }
  });

  if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT && orders.length > 0) {
    console.log('Order format analysis:', stats);
  }

  return stats;
};

// Constants
const SHOPIFY_CONFIG = {
  DEFAULT_LIMIT: 50,
  PAGINATION_HEADER: 'link',
  NEXT_LINK_REGEX: /<(https:[^>]+)>; rel="next"/,
  DEBUG_ORDER_FORMAT: process.env.NODE_ENV === 'development',
} as const;

// Utility functions
const getShopifyHeaders = (): Record<string, string> => {
  // const shopifyApiPassword = '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';
  const shopifyApiPassword = '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';
  // const { shopifyApiPassword: shopifyApiPassword2 } = selectGeneralConfig(
  //   store.getState()
  // );
  return {
    'X-Shopify-Access-Token': shopifyApiPassword,
    'Content-Type': 'application/json',
  };
};

const getShopifyBaseUrl = (): string => {
  return process.env.SHOPIFY_BASE_URL || '';
};

const dispatchProgress = (payload: OrderItem[]): void => {
  store.dispatch({ type: 'items/get.progress', payload });
};

const handleRequestError = (
  error: any,
  url?: string,
  suppressErrors?: boolean
): void => {
  if (suppressErrors) return;

  const message = url ? `${error.toString()}: ${url}` : error.toString();
  store.dispatch({ type: 'errors/add', payload: { message } });
};

// Core HTTP client
const axiosRequest = async <T>(
  params: AxiosRequestConfig,
  suppressErrors?: boolean
): Promise<AxiosResponse<T> | undefined> => {
  store.dispatch({ type: 'requests/increment' });

  try {
    const response = await axios(params);
    return response;
  } catch (error) {
    handleRequestError(error, params.url, suppressErrors);
    if (!suppressErrors) throw error;
    return undefined;
  } finally {
    store.dispatch({ type: 'requests/decrement' });
  }
};

// URL builders
const buildOrdersUrl = (params: string[]): string => {
  const baseUrl = getShopifyBaseUrl();
  return `${baseUrl}/orders.json?${params.join('&')}`;
};

const buildOrderUrl = (orderId: string): string => {
  const baseUrl = getShopifyBaseUrl();
  return `${baseUrl}/orders.json?name=${orderId}&status=any`;
};

const buildCreateOrderUrl = (): string => {
  const baseUrl = getShopifyBaseUrl();
  return `${baseUrl}/orders.json`;
};

const buildDeleteOrderUrl = (id: string): string => {
  const baseUrl = getShopifyBaseUrl();
  return `${baseUrl}/orders/${id}.json`;
};

// Order processing helpers
const processOrdersBatch = async (
  url: string,
  onProgress?: (payload: OrderItem[]) => void
): Promise<{ orders: OrderItem[]; nextUrl?: string }> => {
  const result = await axiosRequest<ShopifyOrdersResponse>({
    method: 'get',
    headers: getShopifyHeaders(),
    url,
  });

  if (!result) {
    throw new Error('Failed to fetch orders - no response received');
  }

  const { data, headers } = result;

  // Analyze order formats in this batch
  const formatStats = analyzeOrderFormats(data.orders);

  const transformedOrders: OrderItem[] = flatten(
    data.orders.map(transformOrder)
  );

  if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
    console.log(`Raw orders received: ${data.orders.length}`);
    console.log(`Transformed orders: ${transformedOrders.length}`);
    console.log('transformedOrders', transformedOrders);
  }

  if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
    console.log(
      `Batch processing complete: ${transformedOrders.length} orders processed, ${formatStats.filteredOut} orders filtered out`
    );
  }

  if (onProgress) {
    onProgress(transformedOrders);
  }

  dispatchProgress(transformedOrders);

  // Extract next URL from pagination headers
  const linkHeader = headers[SHOPIFY_CONFIG.PAGINATION_HEADER] || '';
  const nextUrlMatch = linkHeader.match(SHOPIFY_CONFIG.NEXT_LINK_REGEX);
  const nextUrl = nextUrlMatch?.[1];

  return {
    orders: transformedOrders,
    nextUrl,
  };
};

const buildOrdersQueryParams = (params: GetOrdersParams): string[] => {
  const fromTime = moment(params.fromDate).format();
  const toTime = moment(params.toDate).add(1, 'day').format();

  return [
    `created_at_min=${fromTime}`,
    `created_at_max=${toTime}`,
    `status=${params.onlyOpen ? 'open' : 'any'}`,
    `limit=${SHOPIFY_CONFIG.DEFAULT_LIMIT}`,
  ];
};

const createOrderPayload = (item: OrderItem): Record<string, any> => {
  const { title, sku, quantity } = item;

  const baseOrder = {
    financial_status: 'paid',
    send_receipt: false,
    line_items: [
      {
        title,
        sku,
        quantity,
        price: 0,
      },
    ],
  };

  if (item.source === 'shopify') {
    return {
      ...baseOrder,
      email: item.email,
      customer: { id: item.shopifyOrder.customer.id },
      shipping_address: item.shopifyOrder.shipping_address,
    };
  }

  // Handle Etsy orders
  const {
    name,
    first_line: address1,
    second_line: address2 = '',
    state: province,
    city,
    zip,
    formatted_address,
    buyer_email: email,
  } = item.etsyReceipt;

  const nameMatch = name.match(/^(.+) (.+)$/) || ['', 'Unknown', 'Customer'];
  const [, first_name, last_name] = nameMatch;
  const country = formatted_address.split(/\n/).pop() || '';

  return {
    ...baseOrder,
    email,
    customer: {
      first_name,
      last_name,
      email,
    },
    shipping_address: {
      first_name,
      last_name,
      address1,
      address2,
      city,
      province,
      country,
      zip,
    },
  };
};

// Main API functions
export const getShopifyOrders = async (
  params: GetOrdersParams
): Promise<OrderItem[]> => {
  console.log('getShopifyOrders called with params:', params);
  const queryParams = buildOrdersQueryParams(params);
  const completeOrders: OrderItem[] = [];

  let nextUrl = buildOrdersUrl(queryParams);
  console.log('Shopify API URL:', nextUrl);

  while (nextUrl) {
    const { orders, nextUrl: newNextUrl } = await processOrdersBatch(
      nextUrl,
      params.onProgress
    );

    completeOrders.push(...orders);
    nextUrl = newNextUrl || '';

    if (SHOPIFY_CONFIG.DEBUG_ORDER_FORMAT) {
      console.log(
        `Batch complete. Total orders so far: ${completeOrders.length}`
      );
    }
  }

  return completeOrders;
};

export const getShopifyOrder = async (
  params: GetOrderParams
): Promise<OrderItem[]> => {
  const url = buildOrderUrl(params.orderId);

  const result = await axiosRequest<ShopifyOrdersResponse>({
    method: 'get',
    headers: getShopifyHeaders(),
    url,
  });

  if (!result) {
    throw new Error('Failed to fetch order - no response received');
  }

  const { data } = result;

  // Transform all orders - filtering is now handled in transformOrder function
  const transformedOrders: OrderItem[] = flatten(
    data.orders.map(transformOrder)
  );

  if (params.onProgress) {
    params.onProgress(transformedOrders);
  }

  dispatchProgress(transformedOrders);
  return transformedOrders;
};

export const createShopifyOrderFromItem = async (
  params: CreateOrderParams
): Promise<OrderItem> => {
  const orderPayload = createOrderPayload(params.item);
  const url = buildCreateOrderUrl();

  const result = await axiosRequest<ShopifyOrderResponse>({
    method: 'post',
    headers: getShopifyHeaders(),
    url,
    data: JSON.stringify({ order: orderPayload }),
  });

  if (!result) {
    throw new Error('Failed to create order - no response received');
  }

  const transformedOrders = transformOrder(result.data.order);
  dispatchProgress(transformedOrders);

  return transformedOrders[0];
};

export const deleteShopifyOrderById = async (id: string): Promise<void> => {
  if (!id) {
    throw new Error('Order ID is required for deletion');
  }

  const url = buildDeleteOrderUrl(id);

  try {
    const result = await axiosRequest({
      method: 'delete',
      headers: getShopifyHeaders(),
      url,
    });

    console.log('Order deleted successfully:', result?.status);
  } catch (error) {
    console.error('Failed to delete order:', error);
    throw error;
  }
};
