1. in panel Settings, set app key string and secret using values from Etsy developer console for app
2. in console, run `v2CreateAuthorizationUrl()`
3. copy logged Access Token and Token Secret for later use in Postman
4. open logged URL in Chrome
5. Authorize app
6. copy displayed verification code for later use in Postman (this is the "Verifier")
7. open Postman
8. create new request
   - POST to `https://openapi.etsy.com/v2/oauth/access_token`
   - set auth
     - OAuth 1.0
     - add data to request body / request URL
     - Signature Method = "HMAC-SHA1"
     - Consumer Key = store app key string
     - Consumer Secret = store app secret
     - Access Token = Access Token from console
     - Token Secret = Token Secret from console
     - Callback URL = "oob"
     - Verifier = Verifier from Etsy post-approval page
     - Timestamp = (blank)
     - Nonce = (blank)
     - Version = "1.0"
     - Realm = (blank)
9. Send request
10. Copy OAuth token and secret from response
11. in panel Settings, paste these values as v2 Token and v2 Token Secret, save
