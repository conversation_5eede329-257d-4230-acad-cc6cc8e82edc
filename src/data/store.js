import { configureStore } from '@reduxjs/toolkit';
import produce from 'immer';
import { uniqBy, sortBy, find, uniqueId } from 'lodash';

const defaultState = {
  items: [],
  itemsFileData: [],
  itemsCustomData: [],
  itemsAiData: [],
  itemsLocalData: [],
  isItemsLocalDataLoaded: false,
  config: {
    general: {
      etsyV2TokenKey: null,
      etsyV2TokenSecret: null,
      etsyApiKey: null,
      etsyApiSecret: null,
      etsyShopId: null,
    },
    ringSpecs: [],
    designsByCode: {},
    postageTypes: [],
  },
  isWorking: false,
  activeRequestsCount: 0,
  errors: [],
};

const rootReducer = (state = defaultState, { type, payload }) =>
  produce(state, (draft) => {
    // console.log({ type, payload });
    switch (type) {
      case 'items/get.progress':
        draft.items = sortBy(
          uniqBy([...payload, ...state.items], 'itemId'),
          'createdTimeLocal'
        );
        break;
      case 'items/getFileData.progress':
        draft.itemsFileData = uniqBy(
          [...payload, ...state.itemsFileData],
          'itemId'
        );
        break;
      case 'items/getFileData.replace':
        draft.itemsFileData = payload;
        break;
      case 'items/getAiData.replace':
        draft.itemsAiData = payload;
        break;
      case 'items/getCustomData.progress':
      case 'items/putCustomData.progress':
        draft.itemsCustomData = uniqBy(
          [...payload, ...state.itemsCustomData],
          'itemId'
        );
        break;
      case 'items/patchCustomData': {
        const { itemId } = payload;
        const item = find(draft.itemsCustomData, { itemId });
        if (!item) {
          draft.itemsCustomData.push(payload);
          break;
        }
        Object.assign(item, payload);
        break;
      }
      case 'items/getLocalData.progress':
        draft.itemsLocalData = uniqBy(
          [...payload, ...state.itemsLocalData],
          'itemId'
        );
        draft.isItemsLocalDataLoaded = true;
        break;
      case 'items/renameItemFile': {
        const { itemId, fileName } = payload;
        const itemFileData = find(draft.itemsFileData, { itemId });
        if (itemFileData) itemFileData.fileName = fileName;
        break;
      }
      case 'config/load':
        draft.config = payload;
        break;
      case 'requests/increment':
        draft.activeRequestsCount += 1;
        break;
      case 'requests/decrement':
        draft.activeRequestsCount -= 1;
        break;
      case 'errors/add': {
        const { message } = payload;
        draft.errors.push({
          id: uniqueId(),
          dismissed: false,
          message,
        });
        break;
      }
      case 'errors/dismiss': {
        const { id } = payload;
        const error = find(draft.errors, { id });
        error.dismissed = true;
        break;
      }
      default:
      // noop
    }
  });

export const store = configureStore({ reducer: rootReducer });
