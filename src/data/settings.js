import { fromPairs } from 'lodash';

export const settings = [
  {
    name: 'zoomPercent',
    label: 'Zoom Percent',
    hint: 'UI zoom level percent (50 to 150, 100 = normal)',
  },
  {
    name: 'configBasePath',
    label: 'Config Folder',
    hint: 'Path to config.xlsx, designs, fonts, etc. (shared)',
  },
  {
    name: 'filesBasePath',
    label: 'Input Files Folder',
    hint: 'Path for per-order data and AI files (shared)',
  },
  {
    name: 'dataBasePath',
    label: 'Local Data File Folder',
    hint: 'Path to itemsLocalData.json (KTHQ only: not shared)',
  },
  {
    name: 'outputBasePath',
    label: 'Output Files Folder',
    hint: 'Path for temporary output PDFs (KTHQ only: not shared)',
  },
  {
    name: 'diameter',
    label: 'Diameter and X-Value',
    hint: 'Sheet name for diameter and x-value',
  },
];

export const PKCE = {
  verifier: 'code_verifier_0001',
  challenge: 'mWws2drqE6cRuWKoYLiwvFoftIOxxy7DjmNhgjtjUtk',
};

export const timeZone = 'America/Vancouver';

export const getSettings = () => {
  const encoded = localStorage.getItem('settings');
  const decoded = encoded ? JSON.parse(encoded) : {};
  return fromPairs(
    settings.map(({ name, defaultValue }) => [
      name,
      decoded[name] ?? defaultValue ?? '',
    ])
  );
};

export const writeSettings = (newSettings) => {
  const existingSettings = getSettings();
  window.localStorage.setItem(
    'settings',
    JSON.stringify({ ...existingSettings, ...newSettings })
  );
};
