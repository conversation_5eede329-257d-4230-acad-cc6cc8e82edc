/* eslint-disable no-console */
import dotenv from 'dotenv';
import axios from 'axios';
import createAuthRefreshInterceptor from 'axios-auth-refresh';
import { Etsy } from 'etsy-ts/v3';

// Load environment variables
dotenv.config();

export const refreshAuthLogic = async (
  apiKey: string,
  refreshToken: string
) => {
  // try {
  //   // Authenticate and get the token
  //   const loginResponse = await axios.post(
  //     'https://knot-core-production.up.railway.app/auth/login',
  //     {
  //       email: '<EMAIL>',
  //       password: 'test123',
  //     }
  //   );

  //   const token = loginResponse.data.token.accessToken;

  //   // Set the Authorization header for the next request
  //   const tokenResponse = await axios.get(
  //     'https://knot-core-production.up.railway.app/data/token',
  //     {
  //       headers: {
  //         Authorization: `Bearer ${token}`,
  //       },
  //     }
  //   );

  //   console.log(tokenResponse.data);
  // } catch (e) {
  //   console.log(e);
  // }

  const response = await axios.request<{
    access_token: string;
    refresh_token: string;
  }>({
    method: 'POST',
    url:
      process.env.ETSY_API_BASE_URL ||
      'https://api.etsy.com/v3/public/oauth/token',
    data: {
      grant_type: 'refresh_token',
      client_id: apiKey,
      refresh_token: refreshToken,
    },
  });

  console.log(`refresh_token :${refreshToken}`);
  console.log(`etsyAccessToken :${response.data.access_token}`);

  localStorage.setItem('etsyAccessToken', response.data.access_token);

  return Promise.resolve(undefined);
};

export const initAuthRefresh = (
  client: Etsy,
  apiKey: string,
  refreshToken: string
) => {
  createAuthRefreshInterceptor(
    client.httpClient.instance,
    async () => {
      console.log('refreshing Etsy access token...');
      return refreshAuthLogic(apiKey, refreshToken);
    },
    { pauseInstanceWhileRefreshing: true }
  );

  // Use refreshed token when retrying the failed request
  client.httpClient.instance.interceptors.request.use(async (request) => {
    const storedAccessToken = localStorage.getItem('etsyAccessToken') || '';
    request.headers.Authorization = `Bearer ${storedAccessToken}`;
    return request;
  });
};
