/* eslint-disable @typescript-eslint/naming-convention */
import { decode } from 'html-entities';
import { capitalize } from 'lodash';
import moment from 'moment-timezone';

import { timeZone } from './settings';
import { parseSku, extractCompleteOrderData } from './skuUtil';

export const transformShopifyOrder = (order) => {
  const transformed = [];

  const {
    line_items,
    created_at,
    shipping_address,
    customer: { email },
    closed_at,
    id,
    name, // this is format of '#KT123456'
    current_subtotal_price: orderValue,
    currency: orderCurrency,
    cancelled_at: cancelledAt,
  } = order;

  if (!shipping_address) return []; // could be add-on order or similar, skipped
  const { first_name, last_name } = shipping_address;

  const isMixedOrder =
    line_items.some((i) => i.title.toLowerCase().includes('engraved')) &&
    line_items.some((i) => !i.title.toLowerCase().includes('engraved'));

  const isCancelled = !!cancelledAt;

  line_items.forEach((lineItem, index) => {
    const orderId = name.replace(/^#/, '');
    const itemId = `${orderId}.${index}`;
    const createdTimeLocal = moment(created_at)
      .tz(timeZone)
      .format('YYYY-MM-DD hh:mm');
    const message = decode(lineItem.note);
    const { quantity, sku, properties = [], title } = lineItem;

    // Skip shipping protection items
    if (
      title.toLowerCase().includes('shipping protection') ||
      (message && message.toLowerCase().includes('shipping protection'))
    ) {
      console.log(
        `[Shopify] Skipping shipping protection item - Title: "${title}", Message: "${message}"`
      );
      return;
    }

    // Debug log for non-shipping protection items
    console.log(
      `[Shopify] Processing item - Title: "${title}", Message: "${message}"`
    );

    // Skip items with titles containing ' - Options' (from memory)
    if (title.includes(' - Options')) {
      console.log(`[Shopify] Skipping options item: ${title}`);
      return;
    }

    const isEngraved = title.toLowerCase().includes('engraved');

    // Get initial values from SKU parsing as fallback
    let { style, color, size, design } = parseSku(sku);

    // Extract properties from properties array (prioritize over SKU parsing)
    let width, inkColor;
    let engravingSide;
    let personalization;
    const customFields = [];

    let outside = {};
    let inside = {};
    if (design) {
      // Strip design prefixes (cs, gs, ss) for the outside object
      const cleanDesign = design.replace(/^(cs|gs|ss)/, '');
      outside = { design: cleanDesign };
    }

    // First pass: Extract key properties and handle both old and new property naming conventions
    // Properties take precedence over SKU parsing for new order format
    // eslint-disable-next-line @typescript-eslint/no-shadow
    properties.forEach(({ name, value }) => {
      if (name[0] === '_') return; // Skip Shopify internal properties

      // Handle legacy property names
      if (name === 'Engraving text') personalization = value;

      // Handle new property format (properties override SKU parsing)
      if (name === 'ring-color') color = value; // Override SKU parsed color
      if (name.includes('ring-size') || name.toLowerCase().includes('size')) {
        // Extract numeric part from size (e.g., "8 (4mm slimmer band)" -> "8", "11" -> "11")
        const sizeMatch = value.toString().match(/(\d{1,2}(?:\.\d)?)/);
        if (sizeMatch) {
          const sizeNum = sizeMatch[1].padStart(2, '0');
          size = `S${sizeNum}`;
          console.log(
            `[Shopify] Formatted size from property "${name}": "${value}" -> ${size}`
          );
        } else {
          console.log(
            `[Shopify] Could not extract numeric size from: "${value}"`
          );
        }
      }
      if (name === 'ring-width') width = value;
      if (name === 'ink-color') inkColor = value;
      // Handle various inside engraving property names
      if (
        name === 'inside-text' ||
        name === 'Inside Engraving' ||
        name === 'Inside :'
      ) {
        inside = { text: value };
        console.log(
          `[Shopify] Set inside text from property "${name}": "${value}"`
        );
      }
      if (name === 'Custom Engraving?' && value === 'Inside Engraving')
        inside = { text: personalization };

      // Handle various outside engraving property names
      if (name === 'Outside Engraving' || name === 'Outside :') {
        outside = { ...outside, text: value };
        console.log(
          `[Shopify] Set outside text from property "${name}": "${value}"`
        );
      }
      if (name === 'Custom Engraving?' && value === 'Outside Engraving')
        outside = { ...outside, text: personalization };

      // Add all non-internal properties to customFields
      customFields.push({
        label: name,
        value,
      });
    });

    // Use enhanced SKU extractor to fill in any missing fields
    const extractedData = extractCompleteOrderData({
      style,
      color,
      size,
      design,
      sku,
      title,
      source: 'shopify',
    });

    // Update fields with extracted data
    style = extractedData.style || style || '-';
    color = extractedData.color || color;
    size = extractedData.size || size;
    design = extractedData.design || design;

    // Use extracted inlay type if inkColor is not already set from properties
    // Priority: 1) ink-color property, 2) SKU extraction
    if (!inkColor && extractedData.inlayType) {
      inkColor = extractedData.inlayType;
      console.log(
        `[Shopify] Set ink color from extracted inlay type: ${extractedData.inlayType}`
      );
    } else if (inkColor) {
      console.log(`[Shopify] Using ink color from properties: ${inkColor}`);
    }

    // Final size formatting check - ensure any numeric size gets S prefix
    if (size && !size.startsWith('S')) {
      // Extract numeric part from size (handles "8 (4mm slimmer band)", "11", etc.)
      const sizeMatch = size.toString().match(/(\d{1,2}(?:\.\d)?)/);
      if (sizeMatch) {
        const sizeNum = sizeMatch[1].padStart(2, '0');
        const originalSize = size;
        size = `S${sizeNum}`;
        console.log(
          `[Shopify] Final size formatting: "${originalSize}" -> ${size}`
        );
      }
    }

    // Update outside object if design was extracted
    if (design && !outside.design) {
      const cleanDesign = design.replace(/^(cs|gs|ss)/, '');
      outside = { ...outside, design: cleanDesign };
    }

    // order items - use the actual filtered line items count
    const orderItemCount = line_items.length;
    const orderItemIndex = index;

    // customer "short name" and target filename
    const shippingShortName = `${capitalize(first_name)}${
      capitalize(last_name)[0]
    }`;

    const targetFileName = `${orderId}-${shippingShortName}${
      orderItemCount > 1 ? `-${orderItemIndex + 1}of${orderItemCount}` : ''
    }`;

    // address and value
    const chitchatsAddress = {
      name: shipping_address.name,
      address_1: shipping_address.address1,
      address_2: shipping_address.address2,
      city: shipping_address.city,
      country_code: shipping_address.country_code,
      phone: shipping_address.phone,
      province_code: shipping_address.province_code,
      postal_code: shipping_address.zip,
    };

    transformed.push({
      // itemId
      itemId,
      id,
      // order
      createdTimeLocal,
      orderId,
      orderItemCount,
      orderItemIndex,
      orderItemNumberOfTotal:
        orderItemCount > 1 ? `${orderItemIndex + 1}/${orderItemCount}` : '',
      isShipped: !!closed_at,
      message,
      isMixedOrder,
      isCancelled,
      orderValue,
      orderCurrency,
      // customer
      name: `${first_name} ${last_name}`,
      shippingShortName,
      email,
      // shipping
      chitchatsAddress,
      // item
      title,
      quantity,
      isEngraved,
      sku,
      style,
      color,
      size,
      design,
      width,
      inkColor,
      customFields,
      personalization,
      outside,
      inside,
      // file
      targetFileName,
      // source data
      source: 'shopify',
      shopifyOrder: order,
      shopifyLineItem: lineItem,
    });
  });

  return transformed;
};
