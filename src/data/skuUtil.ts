import { find } from 'lodash';

import { store } from './store';
import { selectSkuValidParams } from './selectors';

export interface SkuParseResult {
  style?: string;
  color?: string;
  size?: string;
  design?: string;
  inlayType?: 'gold' | 'silver' | 'clear' | 'none';
}

/**
 * Extract inlay type from design prefix or SKU patterns
 */
const extractInlayType = (
  sku: string,
  design?: string
): 'gold' | 'silver' | 'clear' | 'none' | undefined => {
  const skuLower = sku.toLowerCase();

  // Check for new inlay patterns
  if (
    skuLower.includes('-gs') ||
    skuLower.includes('gs') ||
    design?.startsWith('gs')
  ) {
    return 'gold';
  }
  if (
    skuLower.includes('-ss') ||
    skuLower.includes('ss') ||
    design?.startsWith('ss')
  ) {
    return 'silver';
  }
  if (
    skuLower.includes('-cs') ||
    skuLower.includes('cs') ||
    design?.startsWith('cs') ||
    skuLower.includes('csclad')
  ) {
    return 'clear';
  }

  // Legacy patterns
  if (skuLower.includes('goldink') || skuLower.includes('gold')) {
    return 'gold';
  }
  if (skuLower.includes('silverink') || skuLower.includes('silver')) {
    return 'silver';
  }

  return undefined;
};

export interface OrderData {
  style?: string;
  color?: string;
  size?: string;
  design?: string;
  sku: string;
  title?: string;
  source: 'shopify' | 'etsy';
}

/**
 * Parse SKU to extract style, color, size, and design information
 *
 * Supports design codes with prefixes:
 * - csfloral -> floral (strips 'cs' prefix) - cs = clear silicone (no inlay)
 * - gsfloral -> floral (strips 'gs' prefix) - gs = gold silicone (gold inlay)
 * - ssfloral -> floral (strips 'ss' prefix) - ss = silver silicone (silver inlay)
 *
 * New inlay patterns:
 * - -gsFili-, -ssFili-, -csFili- (gold, silver, clear inlay with design)
 * - csClad (clear silicone with no color inlay)
 *
 * Style pattern updates:
 * - CF4 and CF-4mm -> C4
 *
 * Example SKU: G-CF6-S11-csfloral-GOLD
 * - style: CF6
 * - size: S11
 * - design: csfloral (stripped to 'floral' for lookup, but keeps original in result)
 * - color: GOLD
 */
export const parseSku = (sku: string): SkuParseResult => {
  const skuValidParams = selectSkuValidParams(store.getState());

  const parsed: SkuParseResult = {};

  if (!sku?.match(/^G-/)) return {};

  const parts = sku.split('-');

  parts.forEach((value) => {
    // Handle design codes with prefixes (cs, gs, ss)
    let processedValue = value;
    let foundParam = find(skuValidParams, { value: processedValue });

    // If not found and it might be a design with prefix, try stripping prefixes
    if (
      !foundParam &&
      (value.startsWith('cs') ||
        value.startsWith('gs') ||
        value.startsWith('ss'))
    ) {
      // Strip the prefix (cs, gs, ss) and try again
      processedValue = value.substring(2); // Remove first 2 characters
      foundParam = find(skuValidParams, { value: processedValue });

      // If found with stripped prefix, use the original value for the parsed result
      // but mark it as a design type
      if (foundParam && foundParam.type === 'design') {
        parsed.design = value; // Keep original value with prefix
        return;
      }
    }

    // If found with original value, use it
    if (foundParam) {
      const paramType = foundParam.type as keyof SkuParseResult;
      if (paramType !== 'inlayType') {
        parsed[paramType] = value;
      }
    }
  });

  // Extract inlay type from the SKU and design
  parsed.inlayType = extractInlayType(sku, parsed.design);

  return parsed;
};

/**
 * Enhanced SKU extractor that fills in missing order data fields
 * Uses SKU parsing as fallback when style, color, design, or size are missing
 */
export const extractCompleteOrderData = (
  orderData: OrderData
): SkuParseResult => {
  const { style, color, size, design, sku, title, source } = orderData;

  // Start with existing data and format size if needed
  let formattedSize = size;

  // Format existing size if it's just a number (e.g., "5" -> "S05")
  if (formattedSize && /^\d{1,2}(\.\d)?$/.test(formattedSize)) {
    const sizeNum = formattedSize.padStart(2, '0');
    formattedSize = `S${sizeNum}`;
    console.log(
      `[SKU Extractor] Formatted existing size: ${formattedSize} (${source})`
    );
  }

  const result: SkuParseResult = {
    style: style || undefined,
    color: color || undefined,
    size: formattedSize || undefined,
    design: design || undefined,
    inlayType: undefined,
  };

  // Parse SKU to get fallback values
  const skuParsed = parseSku(sku);

  // Fill in missing fields from SKU parsing
  if (!result.style && skuParsed.style) {
    result.style = skuParsed.style;
    console.log(
      `[SKU Extractor] Filled missing style from SKU: ${skuParsed.style} (${source})`
    );
  }

  if (!result.color && skuParsed.color) {
    result.color = skuParsed.color;
    console.log(
      `[SKU Extractor] Filled missing color from SKU: ${skuParsed.color} (${source})`
    );
  }

  if (!result.size && skuParsed.size) {
    result.size = skuParsed.size;
    console.log(
      `[SKU Extractor] Filled missing size from SKU: ${skuParsed.size} (${source})`
    );
  }

  if (!result.design && skuParsed.design) {
    result.design = skuParsed.design;
    console.log(
      `[SKU Extractor] Filled missing design from SKU: ${skuParsed.design} (${source})`
    );
  }

  // Always extract inlay type from SKU parsing
  if (skuParsed.inlayType) {
    result.inlayType = skuParsed.inlayType;
    console.log(
      `[SKU Extractor] Extracted inlay type from SKU: ${skuParsed.inlayType} (${source})`
    );
  }

  // Enhanced style extraction for descriptive SKU patterns
  if (!result.style && sku) {
    const skuLower = sku.toLowerCase();
    let inferredStyle: string | undefined;

    // Handle descriptive SKU patterns like "ComfortFit-6mm-GreenEnchanted-12" and "CF-4mm-StarPurple-11"
    if (skuLower.includes('comfortfit') && skuLower.includes('6mm')) {
      inferredStyle = 'CF6';
    } else if (skuLower.includes('comfortfit') && skuLower.includes('4mm')) {
      inferredStyle = 'CF4'; // New pattern: CF4 and CF-4mm -> C4
    } else if (
      (skuLower.startsWith('cf-') || skuLower.includes('-cf-')) &&
      skuLower.includes('4mm')
    ) {
      inferredStyle = 'CF4'; // New pattern: CF4 and CF-4mm -> C4
    } else if (
      (skuLower.startsWith('cf-') || skuLower.includes('-cf-')) &&
      skuLower.includes('6mm')
    ) {
      inferredStyle = 'CF6';
    } else if (skuLower.includes('domed') && skuLower.includes('6mm')) {
      inferredStyle = 'D6';
    } else if (skuLower.includes('stepped') && skuLower.includes('comfort')) {
      inferredStyle = 'SCF';
    }
    // Legacy patterns
    else if (skuLower.includes('d6-')) {
      inferredStyle = 'D6';
    } else if (skuLower.includes('scf-')) {
      inferredStyle = 'SCF';
    } else if (skuLower.includes('cf6-') || skuLower.includes('c6')) {
      inferredStyle = 'CF6';
    } else if (skuLower.includes('cf4-') || skuLower.includes('c4')) {
      inferredStyle = 'CF4'; // New pattern: CF4 -> C4
    }

    if (inferredStyle) {
      result.style = inferredStyle;
      console.log(
        `[SKU Extractor] Inferred style from SKU pattern: ${inferredStyle} (${source})`
      );
    }
  }

  // Enhanced size extraction from descriptive SKUs
  if (!result.size && sku) {
    // Look for size patterns in SKU like "ComfortFit-6mm-GreenEnchanted-12" or "ComfortFit-6mm-Black-R11"
    let sizeMatch = sku.match(/[-_]R(\d{1,2}(?:\.\d)?)(?:$|[-_])/i); // R11, R8, etc.
    if (!sizeMatch) {
      sizeMatch = sku.match(/[-_](\d{1,2}(?:\.\d)?)(?:$|[-_])/); // 12, 8, etc.
    }
    if (sizeMatch) {
      const sizeNum = sizeMatch[1].padStart(2, '0');
      result.size = `S${sizeNum}`;
      console.log(
        `[SKU Extractor] Extracted size from SKU pattern: ${result.size} (${source})`
      );
    }
  }

  // Enhanced color extraction from descriptive SKUs
  if (!result.color && sku) {
    const skuValidParams = selectSkuValidParams(store.getState());
    const validColors = skuValidParams.filter(
      (param) => param.type === 'color'
    );
    const skuLower = sku.toLowerCase();

    // Look for color patterns in descriptive SKUs and validate against config
    const colorPatterns = [
      'green',
      'greenenchanted',
      'stargreen',
      'gold',
      'goldenchanted',
      'stargold',
      'silver',
      'silverenchanted',
      'starsilver',
      'black',
      'blackenchanted',
      'starblack',
      'blue',
      'blueenchanted',
      'starblue',
      'red',
      'redenchanted',
      'starred',
      'purple',
      'purpleenchanted',
      'starpurple',
      'pink',
      'pinkenchanted',
      'starpink',
      'orange',
      'orangeenchanted',
      'starorange',
      'yellow',
      'yellowenchanted',
      'staryellow',
      'brown',
      'brownenchanted',
      'starbrown',
      'white',
      'whiteenchanted',
      'starwhite',
      'gray',
      'grey',
      'grayenchanted',
      'greyenchanted',
      'stargray',
      'stargrey',
      // Additional compound colors
      'darkbronze',
      'lightbronze',
      'bronze',
      'darkgold',
      'lightgold',
      'rosegold',
      'rose',
      'darksilver',
      'lightsilver',
      'darkblue',
      'lightblue',
      'navy',
      'darkgreen',
      'lightgreen',
      'darkred',
      'lightred',
      'darkgray',
      'lightgray',
      'darkgrey',
      'lightgrey',
      'nlights',
      'nlight',
      'northern',
      'lights',
    ];

    for (const pattern of colorPatterns) {
      if (skuLower.includes(pattern)) {
        // Extract base color name (remove prefixes/suffixes)
        let baseColor = pattern.replace(/^star/, '').replace(/enchanted$/, '');

        // Handle compound colors - try multiple matching strategies
        let validColor = validColors.find((color) => {
          const colorLower = color.value.toLowerCase();
          const baseLower = baseColor.toLowerCase();

          return (
            // Exact match
            colorLower === baseLower ||
            // Config color contains pattern (e.g., "Dark Bronze" contains "darkbronze")
            colorLower.replace(/\s+/g, '') === baseLower ||
            // Pattern contains config color
            baseLower.includes(colorLower.replace(/\s+/g, '')) ||
            // Config color contains pattern
            colorLower.includes(baseLower) ||
            // Handle special cases
            (baseLower === 'nlights' && colorLower.includes('northern')) ||
            (baseLower === 'nlights' && colorLower.includes('lights')) ||
            (baseLower === 'bronze' && colorLower.includes('bronze')) ||
            (baseLower === 'rose' && colorLower.includes('rose'))
          );
        });

        if (validColor) {
          result.color = validColor.value;
          console.log(
            `[SKU Extractor] Extracted color from SKU pattern "${pattern}": ${result.color} (${source})`
          );
          break;
        } else {
          console.log(
            `[SKU Extractor] Color pattern "${pattern}" found in SKU but no matching config color (${source})`
          );
        }
      }
    }
  }

  // Enhanced design extraction from descriptive SKUs
  if (!result.design && sku) {
    const skuValidParams = selectSkuValidParams(store.getState());
    const validDesigns = skuValidParams.filter(
      (param) => param.type === 'design'
    );
    const skuLower = sku.toLowerCase();

    // Look for design patterns in descriptive SKUs and validate against config
    const designPatterns = [
      'enchanted',
      'geometric',
      'floral',
      'celtic',
      'tribal',
      'vintage',
      'modern',
      'classic',
      'nature',
      'abstract',
      'minimalist',
      'ornate',
    ];

    for (const pattern of designPatterns) {
      if (skuLower.includes(pattern)) {
        // Find matching valid design from config
        const validDesign = validDesigns.find(
          (design) =>
            design.value.toLowerCase() === pattern.toLowerCase() ||
            design.value.toLowerCase().includes(pattern.toLowerCase()) ||
            pattern.toLowerCase().includes(design.value.toLowerCase())
        );

        if (validDesign) {
          result.design = validDesign.value;
          console.log(
            `[SKU Extractor] Extracted design from SKU pattern: ${result.design} (${source})`
          );
          break;
        }
      }
    }
  }

  // Try to extract additional info from title if available
  if (title) {
    const titleLower = title.toLowerCase();

    // Extract size from title if missing (common patterns)
    if (!result.size) {
      const sizeMatch = title.match(/\b(size\s+)?(\d{1,2}(?:\.\d)?)\b/i);
      if (sizeMatch) {
        const sizeNum = sizeMatch[2].padStart(2, '0');
        result.size = `S${sizeNum}`;
        console.log(
          `[SKU Extractor] Extracted size from title: ${result.size} (${source})`
        );
      }
    }

    // Extract color from title if missing (validate against config)
    if (!result.color) {
      const skuValidParams = selectSkuValidParams(store.getState());
      const validColors = skuValidParams.filter(
        (param) => param.type === 'color'
      );

      // Find color in title that matches valid colors from config
      const colorMatch = validColors.find((color) =>
        titleLower.includes(color.value.toLowerCase())
      );

      if (colorMatch) {
        result.color = colorMatch.value;
        console.log(
          `[SKU Extractor] Extracted color from title: ${result.color} (${source})`
        );
      }
    }
  }

  return result;
};
