/* eslint-disable no-console */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/naming-convention */
import dotenv from 'dotenv';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import { fromPairs, flatten, groupBy, omitBy } from 'lodash';
import fs from 'fs';
import { exec } from 'child_process';
import path from 'path';
import { GoogleSpreadsheet } from 'google-spreadsheet';
import xlsx from 'xlsx';

// Load environment variables
dotenv.config();
import { getSettings } from './settings';
import { convertUnitToPx } from '../lib/utils';
import { store } from './store';
import { selectGeneralConfig, selectItemsLocalData } from './selectors';
import { OrderItem } from './types';
import {
  getEtsyReceipts,
  getEtsyReceipt,
  requestAuthorizationCode,
  requestAccessToken,
} from './etsyActions';
import { getShopifyOrders, getShopifyOrder } from './shopifyActions';

const axiosRequest = async <T>(
  params: AxiosRequestConfig,
  suppressErrors?: boolean
): Promise<AxiosResponse<T> | undefined> => {
  store.dispatch({ type: 'requests/increment' });
  return axios(params)
    .catch((error) => {
      if (suppressErrors) return undefined;
      const { url } = params;
      const message = `${error.toString()}: ${url}`;
      store.dispatch({ type: 'errors/add', payload: { message } });
      throw error;
    })
    .finally(() => {
      store.dispatch({ type: 'requests/decrement' });
    });
};

export const getItemsCustomData = async ({
  itemIds,
}: {
  itemIds: string[];
}) => {
  if (itemIds.length === 0) return;
  const { customApiUrl, customApiKey } = selectGeneralConfig(store.getState());

  if (!customApiKey) return; // not using API-based persistence, skip

  const result = await axiosRequest<{ items: any }>({
    // TODO: type items
    method: 'get',
    headers: { 'X-Custom-Api-Key': customApiKey },
    url: `${customApiUrl}/items?itemIds=${itemIds.join(',')}`,
  });

  if (!result?.data) throw new Error('no data');
  const {
    data: { items },
  } = result;

  store.dispatch({ type: 'items/getCustomData.progress', payload: items });
};

export const getItems = async ({
  fromDate,
  toDate,
  onlyOpen,
  canceled,
}: {
  fromDate: string;
  toDate: string;
  onlyOpen: boolean;
  canceled: boolean;
}) => {
  const onProgress = (payload: OrderItem[]) => {
    getItemsCustomData({ itemIds: payload.map(({ itemId }) => itemId) });
  };

  const results = await Promise.all([
    getEtsyReceipts({
      fromDate,
      toDate,
      onlyOpen,
      canceled,
      onProgress,
    }),
    getShopifyOrders({
      fromDate,
      toDate,
      onlyOpen,
      canceled,
      onProgress,
    }),
  ]);

  return flatten(results);
};

export const getOrderItems = ({ orderId }: { orderId: string }) => {
  const onProgress = (payload: OrderItem[]) => {
    getItemsCustomData({ itemIds: payload.map(({ itemId }) => itemId) });
  };

  const getOrderFn = orderId.match('KT') ? getShopifyOrder : getEtsyReceipt;

  return getOrderFn({ orderId, onProgress });
};

(window as any).requestAuthorizationCode = requestAuthorizationCode;
(window as any).requestAccessToken = requestAccessToken;

export const loadConfigFromLocal = async () => {
  const filePath = path.join(__dirname, '../assets', 'app-config.xlsx');
  const fileBuffer = fs.readFileSync(filePath);
  const workbook = xlsx.read(fileBuffer, { type: 'buffer' });

  const scrub = (o: Record<string, any>) => {
    return omitBy(o, (_, key) => key[0] === '_');
  };

  const sheetToJSON = (sheetName: string): Record<string, any>[] => {
    const sheet = workbook.Sheets[sheetName];
    return xlsx.utils.sheet_to_json(sheet, { raw: true }) as Record<
      string,
      string
    >[];
  };

  // General
  const generalRows = sheetToJSON('General');
  const general = fromPairs(generalRows.map(({ key, value }) => [key, value]));

  // Ring Specs
  const rawRingSpecs = sheetToJSON('Rings');
  const ringSpecs = rawRingSpecs.map((spec) => {
    return {
      ...scrub(spec),
      inside: {
        surfaceLength: convertUnitToPx.mm(spec.surfaceLengthMmInside),
        surfaceWidth: convertUnitToPx.mm(spec.surfaceWidthMmInside),
        designWidth: convertUnitToPx.mm(spec.designWidthMmInside),
        textWidth: convertUnitToPx.mm(spec.textWidthMmInside),
        power: spec.powerInside,
      },
      outside: {
        surfaceLength: convertUnitToPx.mm(spec.surfaceLengthMmOutside),
        surfaceWidth: convertUnitToPx.mm(spec.surfaceWidthMmOutside),
        designWidth: convertUnitToPx.mm(spec.designWidthMmOutside),
        textWidth: convertUnitToPx.mm(spec.textWidthMmOutside),
        power: spec.powerOutside,
      },
    };
  });

  // Designs
  const designSpecs = sheetToJSON('Designs').map(scrub);

  // Colors
  const colors = sheetToJSON('Colors').map(scrub);

  // Postage Types
  const postageTypes = sheetToJSON('Postage').map(scrub);

  // Inlay Designs
  const inlayDesigns = sheetToJSON('inlay').map(scrub);
  const inlayGoldDesigns = sheetToJSON('InOutInlay').map(scrub);
  const inlaySilverDesigns = sheetToJSON('SilverInlay').map(scrub);

  store.dispatch({
    type: 'config/load',
    payload: {
      ringSpecs,
      general,
      designSpecs,
      colors,
      postageTypes,
      inlayDesigns,
      inlayGoldDesigns,
      inlaySilverDesigns,
    },
  });
};

const pk =
*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
    /\\n/g,
    '\n'
  );

export const loadConfigFromCloud = async () => {
  const DOC_ID = '1QUuFD84Q1BcByVgVTt7BGH_K89xkykUja4_QKxlXijk';
  const doc = new GoogleSpreadsheet(DOC_ID);

  await doc.useServiceAccountAuth({
    client_email: '<EMAIL>',
    private_key: pk,
  });

  await doc.loadInfo();

  const scrub = (o: Record<string, any>) => {
    return omitBy(o, (_, key) => key[0] === '_');
  };

  // General
  const generalRows = await doc.sheetsByTitle.General.getRows();
  const general = fromPairs(generalRows.map(({ key, value }) => [key, value]));

  // Ring Specs
  const rawRingSpecs = await doc.sheetsByTitle.Rings.getRows();
  const ringSpecs = rawRingSpecs.map((spec) => {
    return {
      ...scrub(spec),
      inside: {
        surfaceLength: convertUnitToPx.mm(spec.surfaceLengthMmInside),
        surfaceWidth: convertUnitToPx.mm(spec.surfaceWidthMmInside),
        designWidth: convertUnitToPx.mm(spec.designWidthMmInside),
        textWidth: convertUnitToPx.mm(spec.textWidthMmInside),
        power: spec.powerInside,
      },
      outside: {
        surfaceLength: convertUnitToPx.mm(spec.surfaceLengthMmOutside),
        surfaceWidth: convertUnitToPx.mm(spec.surfaceWidthMmOutside),
        designWidth: convertUnitToPx.mm(spec.designWidthMmOutside),
        textWidth: convertUnitToPx.mm(spec.textWidthMmOutside),
        power: spec.powerOutside,
      },
    };
  });

  // Designs
  const designSpecs = await doc.sheetsByTitle.Designs.getRows().then((rows) =>
    rows.map(scrub)
  );

  // Colors
  const colors = await doc.sheetsByTitle.Colors.getRows().then((rows) =>
    rows.map(scrub)
  );

  // Postage Types
  const postageTypes = await doc.sheetsByTitle.Postage.getRows().then((rows) =>
    rows.map(scrub)
  );

  // Inlay Designs
  const inlayDesigns = await doc.sheetsByTitle.Inlay.getRows().then((rows) =>
    rows.map(scrub)
  );
  // Inlay Designs
  const inlayGoldDesigns = await doc.sheetsByTitle.InOutInlay.getRows().then(
    (rows) => rows.map(scrub)
  );

  // Inlay Designs
  const inlaySilverDesigns = await doc.sheetsByTitle.SilverInlay.getRows().then(
    (rows) => rows.map(scrub)
  );

  store.dispatch({
    type: 'config/load',
    payload: {
      ringSpecs,
      general,
      designSpecs,
      colors,
      postageTypes,
      inlayDesigns,
      inlayGoldDesigns,
      inlaySilverDesigns,
    },
  });
};

export const scanForFiles = async () => {
  const { filesBasePath } = getSettings();
  const fileNames = fs.readdirSync(filesBasePath);

  const fileNamePattern = /^.*?((E|KT)\d+).*?(\d+of\d+)?\.(ai|json)$/;
  const orderIdElement = 1;
  const nOfNElement = 3;
  const extensionElement = 4;

  const validFileNames = fileNames.filter((fileName) =>
    fileName.match(fileNamePattern)
  );

  const allData = validFileNames.map((fileName) => {
    const match = fileName.match(fileNamePattern);
    if (!match)
      return {
        fileParsingError: true,
      };
    const orderId = match[orderIdElement];
    const nOfN = match[nOfNElement];
    const orderItemIndex = nOfN ? Number(nOfN[0].match(/^\d+/)) - 1 : 0;
    const itemId = `${orderId}.${orderItemIndex}`;
    const extension = match[extensionElement];

    let data = {
      itemId,
      fileType: extension,
      fileName: undefined as string | undefined,
      aiFileName: undefined as string | undefined,
    };

    try {
      if (extension === 'json') {
        const fileContent = fs.readFileSync(
          path.join(filesBasePath, fileName),
          'utf-8'
        );
        data = { ...data, ...JSON.parse(fileContent) };
        data.fileName = fileName;
      } else if (extension === 'ai') {
        data.aiFileName = fileName;
      }
    } catch (error) {
      return {
        fileParsingError: true,
      };
    }

    return data;
  });

  const dataByFileType = groupBy(allData, 'fileType');

  store.dispatch({
    type: 'items/getFileData.replace',
    payload: dataByFileType.json || [],
  });
  store.dispatch({
    type: 'items/getAiData.replace',
    payload: dataByFileType.ai || [],
  });
};

// const saveDataFile = async (fileName: string, itemData: any) => {
//   const { filesBasePath } = getSettings();

//   fs.writeFileSync(
//     path.join(filesBasePath, fileName),
//     JSON.stringify(itemData),
//     (err: any) => {
//       if (err) throw err;
//     }
//   );

//   const payload = [{ ...itemData, fileName, fileType: 'json' }];

//   store.dispatch({ type: 'items/getFileData.progress', payload });
// };

// export const putItemData = async ({ fileName, ...item }) => {
//   const { customApiUrl, customApiKey } = selectGeneralConfig(store.getState());

//   if (customApiKey) {
//     // API-based persistence
//     const { itemId } = item;
//     await axiosRequest({
//       method: 'put',
//       headers: { 'X-Custom-Api-Key': customApiKey },
//       url: `${customApiUrl}/item/${itemId}`,
//       data: item,
//     });

//     store.dispatch({
//       type: 'items/putCustomData.progress',
//       payload: [item],
//     });
//   } else {
//     // file-based persistence
//     store.dispatch({ type: 'requests/increment' });
//     await saveDataFile(fileName, item);
//     store.dispatch({ type: 'requests/decrement' });
//   }
// };

export const patchItemData = async (item: OrderItem) => {
  const { customApiUrl, customApiKey } = selectGeneralConfig(store.getState());

  const {
    itemId,
    fileName, // dropped in API call
    ...restItem
  } = item;

  if (!customApiKey) {
    // file-based persistence
    throw new Error('file persistence deprecated');
    // store.dispatch({ type: 'requests/increment' });
    // await saveDataFile(fileName, item); // pre-Chitchats, item is always complete data; dispatches items/getFileData.progress
    // store.dispatch({ type: 'requests/decrement' });
  } else {
    // API-based persistence
    await axiosRequest({
      method: 'patch',
      headers: { 'X-Custom-Api-Key': customApiKey },
      url: `${customApiUrl}/item/${itemId}`,
      data: restItem,
    });
    store.dispatch({
      type: 'items/patchCustomData',
      payload: item,
    });
  }
};

export const maybeRenameItemFile = async ({
  itemId,
  fileName,
}: {
  itemId: string;
  fileName: string;
}) => {
  const { filesBasePath } = getSettings();
  const { fileNameDonePrefix } = selectGeneralConfig(store.getState());
  if (!fileName || !fileNameDonePrefix) return;

  const newFileName = `${fileNameDonePrefix}${fileName}`;

  fs.renameSync(
    path.join(filesBasePath, fileName),
    path.join(filesBasePath, newFileName)
  );

  store.dispatch({
    type: 'items/renameItemFile',
    payload: { itemId, fileName: newFileName },
  });
};

export const openFile = async (filePath: string) => {
  exec(`${process.platform === 'darwin' ? 'open ' : ''}"${filePath}"`);
};

export const saveItemLocalData = async (itemData: Record<string, any>) => {
  // DEPRECATED

  store.dispatch({ type: 'items/getLocalData.progress', payload: [itemData] });

  window.setTimeout(() => {
    const { dataBasePath } = getSettings();

    fs.writeFileSync(
      path.join(dataBasePath, 'itemsLocalData.json'),
      JSON.stringify(selectItemsLocalData(store.getState())),
      ((err: any) => {
        if (err) throw err;
      }) as any
    );
  }, 0);
};

export const loadLocalDataFromFile = async () => {
  const { dataBasePath } = getSettings();

  try {
    const fileContent = fs.readFileSync(
      path.join(dataBasePath, 'itemsLocalData.json'),
      'utf-8'
    );
    store.dispatch({
      type: 'items/getLocalData.progress',
      payload: JSON.parse(fileContent),
    });
  } catch (error) {
    // do nothing
  }
};

export const dismissError = (id: string) => {
  store.dispatch({
    type: 'errors/dismiss',
    payload: { id },
  });
};

export const createChitchatsBatch = async () => {
  const { chitchatsClientId, chitchatsApiToken } = selectGeneralConfig(
    store.getState()
  );

  const result = await axiosRequest<{ batch: { id: string } }>({
    method: 'post',
    headers: {
      Authorization: chitchatsApiToken,
      'Content-Type': 'application/json',
    },
    url: `${process.env.CHITCHATS_API_BASE_URL}/clients/${chitchatsClientId}/batches`,
  });

  // return {
  //   id: `BATCH${uniqueId()}`,
  // };
  if (!result) throw new Error('No result');

  return result.data.batch; // id is numeric batch ID
};

export const deleteChitchatsBatchByIdIfEmpty = async (batchId: string) => {
  const { chitchatsClientId, chitchatsApiToken } = selectGeneralConfig(
    store.getState()
  );

  try {
    await axiosRequest(
      {
        method: 'delete',
        headers: {
          Authorization: chitchatsApiToken,
          'Content-Type': 'application/json',
        },
        url: `${process.env.CHITCHATS_API_BASE_URL}/clients/${chitchatsClientId}/batches/${batchId}`,
      },
      true
    );
    return true;
  } catch (error) {
    return false;
  }
};
