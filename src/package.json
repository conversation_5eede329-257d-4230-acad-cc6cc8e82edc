{"name": "knotheory-app", "productName": "KTHQ", "version": "1.1.0", "description": "Knotheory HQ (based on electron-react-boilerplace)", "main": "./main.prod.js", "author": {"name": "Knot Theory", "email": "<EMAIL>"}, "scripts": {"electron-rebuild": "node -r ../.erb/scripts/BabelRegister.js ../.erb/scripts/ElectronRebuild.js", "postinstall": "yarn electron-rebuild"}, "dependencies": {"dotenv": "^17.0.0", "pdfkit": "0.13.0", "svg-to-pdfkit": "0.1.8"}}