{"name": "knotheory-app", "productName": "KTHQ", "description": "Knotheory HQ (based on electron-react-boilerplace)", "scripts": {"build": "concurrently \"yarn build:main\" \"yarn build:renderer\"", "build:main": "cross-env NODE_ENV=production webpack --config ./.erb/configs/webpack.config.main.prod.babel.js", "build:renderer": "cross-env NODE_ENV=production webpack --config ./.erb/configs/webpack.config.renderer.prod.babel.js", "rebuild": "electron-rebuild --parallel --types prod,dev,optional --module-dir src", "lint": "cross-env NODE_ENV=development eslint . --cache --ext .js,.jsx,.ts,.tsx", "package": "del-cli src/dist && yarn build && electron-builder build --publish never", "package:adhoc": "del-cli src/dist && yarn build && CSC_IDENTITY_AUTO_DISCOVERY=false electron-builder build --publish never", "package:win": "del-cli src/dist && yarn build && electron-builder build --publish never", "postinstall": "node -r @babel/register .erb/scripts/CheckNativeDep.js && electron-builder install-app-deps && yarn cross-env NODE_ENV=development webpack --config ./.erb/configs/webpack.config.renderer.dev.dll.babel.js && opencollective-postinstall && yarn-deduplicate yarn.lock", "start": "node -r @babel/register ./.erb/scripts/CheckPortInUse.js && yarn start:renderer", "start:main": "cross-env NODE_ENV=development electron -r ./.erb/scripts/BabelRegister ./src/main.dev.ts", "start:renderer": "cross-env NODE_ENV=development webpack serve --config ./.erb/configs/webpack.config.renderer.dev.babel.js", "test": "jest", "watch": "tsc --watch"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["cross-env NODE_ENV=development eslint --cache"], "{*.json,.{babelrc,eslintrc,prettierrc}}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --parser json --write"], "*.{css,scss}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --single-quote --write"], "*.{html,md,yml}": ["prettier --ignore-path .es<PERSON><PERSON><PERSON> --single-quote --write"]}, "build": {"productName": "KTHQ", "appId": "org.knotheory.kthq", "files": ["dist/", "node_modules/", "index.html", "main.prod.js", "main.prod.js.map", "package.json"], "afterSign": ".erb/scripts/Notarize.js", "mac": {"target": ["dmg"], "type": "distribution", "hardenedRuntime": true, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist", "gatekeeperAssess": false}, "dmg": {"contents": [{"x": 130, "y": 220}, {"x": 410, "y": 220, "type": "link", "path": "/Applications"}]}, "win": {"target": ["nsis"]}, "linux": {"target": ["AppImage"], "category": "Development"}, "directories": {"app": "src", "buildResources": "assets", "output": "release"}, "extraResources": ["./assets/**", "./assets/fonts/**"]}, "author": {"name": "Knot Theory", "email": "<EMAIL>"}, "jest": {"testURL": "http://localhost/", "moduleNameMapper": {"\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "<rootDir>/.erb/mocks/fileMock.js", "\\.(css|less|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["js", "jsx", "ts", "tsx", "json"], "moduleDirectories": ["node_modules", "src/node_modules"], "setupFiles": ["./.erb/scripts/CheckBuildsExist.js"]}, "devDependencies": {"@babel/core": "^7.12.9", "@babel/plugin-proposal-class-properties": "^7.12.1", "@babel/plugin-proposal-decorators": "^7.12.1", "@babel/plugin-proposal-do-expressions": "^7.12.1", "@babel/plugin-proposal-export-default-from": "^7.12.1", "@babel/plugin-proposal-export-namespace-from": "^7.12.1", "@babel/plugin-proposal-function-bind": "^7.12.1", "@babel/plugin-proposal-function-sent": "^7.12.1", "@babel/plugin-proposal-json-strings": "^7.12.1", "@babel/plugin-proposal-logical-assignment-operators": "^7.12.1", "@babel/plugin-proposal-nullish-coalescing-operator": "^7.12.1", "@babel/plugin-proposal-optional-chaining": "^7.12.7", "@babel/plugin-proposal-pipeline-operator": "^7.12.1", "@babel/plugin-proposal-throw-expressions": "^7.12.1", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-react-constant-elements": "^7.12.1", "@babel/plugin-transform-react-inline-elements": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.7", "@babel/preset-react": "^7.12.7", "@babel/preset-typescript": "^7.12.7", "@babel/register": "^7.12.1", "@pmmmwh/react-refresh-webpack-plugin": "^0.4.3", "@teamsupercell/typings-for-css-modules-loader": "^2.4.0", "@testing-library/jest-dom": "^5.11.6", "@testing-library/react": "^11.2.2", "@types/enzyme": "^3.10.5", "@types/enzyme-adapter-react-16": "^1.0.6", "@types/google-spreadsheet": "^3.3.1", "@types/history": "4.7.6", "@types/jest": "^26.0.15", "@types/lodash": "4.14.74", "@types/node": "14.14.10", "@types/react": "^16.9.44", "@types/react-dom": "^16.9.9", "@types/react-router-dom": "^5.1.6", "@types/react-test-renderer": "^16.9.3", "@types/webpack-env": "^1.15.2", "@typescript-eslint/eslint-plugin": "^4.8.1", "@typescript-eslint/parser": "^4.8.1", "babel-eslint": "^10.1.0", "babel-jest": "^26.1.0", "babel-loader": "^8.2.2", "babel-plugin-dev-expression": "^0.2.2", "babel-plugin-transform-react-remove-prop-types": "^0.4.24", "browserslist-config-erb": "^0.0.1", "chalk": "^4.1.0", "concurrently": "^5.3.0", "core-js": "^3.6.5", "cross-env": "^7.0.2", "css-loader": "^5.0.1", "css-minimizer-webpack-plugin": "^1.1.5", "del-cli": "^5.1.0", "detect-port": "^1.3.0", "electron": "^11.0.1", "electron-builder": "^22.10.5", "electron-devtools-installer": "^3.1.1", "electron-notarize": "^1.0.0", "electron-rebuild": "^2.3.2", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.3", "enzyme-to-json": "^3.5.0", "eslint": "^7.5.0", "eslint-config-airbnb": "^18.2.0", "eslint-config-airbnb-typescript": "^12.0.0", "eslint-config-erb": "^2.0.0", "eslint-config-prettier": "^6.11.0", "eslint-import-resolver-webpack": "^0.13.0", "eslint-plugin-compat": "^3.8.0", "eslint-plugin-import": "^2.22.0", "eslint-plugin-jest": "^24.1.3", "eslint-plugin-jsx-a11y": "6.4.1", "eslint-plugin-prettier": "^3.1.4", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.20.6", "eslint-plugin-react-hooks": "^4.0.8", "file-loader": "^6.0.0", "husky": "^4.2.5", "identity-obj-proxy": "^3.0.0", "jest": "^26.1.0", "lint-staged": "^10.2.11", "mini-css-extract-plugin": "^1.3.1", "opencollective-postinstall": "^2.0.3", "prettier": "^2.0.5", "react-refresh": "^0.9.0", "react-test-renderer": "^17.0.1", "rimraf": "^3.0.0", "sass": "^1.69.0", "sass-loader": "^10.1.0", "style-loader": "^2.0.0", "terser-webpack-plugin": "^5.0.3", "typescript": "^4.0.5", "url-loader": "^4.1.0", "webpack": "^5.5.1", "webpack-bundle-analyzer": "^4.1.0", "webpack-cli": "^4.2.0", "webpack-dev-server": "^3.11.0", "webpack-merge": "^5.4.0", "yarn-deduplicate": "^3.1.0"}, "dependencies": {"@reduxjs/toolkit": "^1.6.0", "ag-grid-community": "^25.3.0", "ag-grid-react": "^25.3.0", "axios": "^0.21.1", "axios-auth-refresh": "^3.3.6", "axios-oauth-1.0a": "^0.3.0", "dotenv": "^16.0.0", "electron-debug": "^3.1.0", "electron-log": "^4.2.4", "electron-updater": "^4.3.4", "etsy-js": "^0.0.6", "etsy-ts": "^3.11.0", "fontkit": "^1.8.1", "google-spreadsheet": "^3.2.0", "history": "^5.0.0", "html-entities": "^2.3.2", "immer": "^9.0.5", "jspdf": "^2.3.1", "lodash": "^4.17.21", "moment": "^2.29.1", "moment-timezone": "^0.5.33", "oauth": "^0.9.15", "oauth-1.0a": "^2.2.6", "pdfkit": "^0.15.0", "react": "^17.0.1", "react-dom": "^17.0.1", "react-redux": "^7.2.4", "react-router-dom": "^5.2.0", "regenerator-runtime": "^0.13.5", "source-map-support": "^0.5.19", "svg-to-pdfkit": "^0.1.8", "svg2pdf.js": "^2.1.0", "transform-loader": "^0.2.4", "xlsx": "^0.17.0"}, "devEngines": {"node": ">=10.x", "npm": ">=6.x", "yarn": ">=1.21.3"}, "browserslist": [], "prettier": {"overrides": [{"files": [".prettier<PERSON>", ".babelrc", ".eslintrc"], "options": {"parser": "json"}}], "singleQuote": true}, "renovate": {"extends": ["bliss"], "baseBranches": ["next"]}, "husky": {"hooks": {"pre-commit": "lint-staged"}}}