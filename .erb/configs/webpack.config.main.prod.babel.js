/**
 * Webpack config for production electron main process
 */

import path from 'path';
import webpack from 'webpack';
import { merge } from 'webpack-merge';
import TerserPlugin from 'terser-webpack-plugin';
import { BundleAnalyzerPlugin } from 'webpack-bundle-analyzer';
import baseConfig from './webpack.config.base';
import CheckNodeEnv from '../scripts/CheckNodeEnv';
import DeleteSourceMaps from '../scripts/DeleteSourceMaps';

CheckNodeEnv('production');
DeleteSourceMaps();

const devtoolsConfig =
  process.env.DEBUG_PROD === 'true'
    ? {
        devtool: 'source-map',
      }
    : {};

export default merge(baseConfig, {
  ...devtoolsConfig,

  mode: 'production',

  target: 'electron-main',

  entry: './src/main.dev.ts',

  output: {
    path: path.join(__dirname, '../../'),
    filename: './src/main.prod.js',
  },

  optimization: {
    minimizer: [
      new TerserPlugin({
        parallel: true,
      }),
    ],
  },

  plugins: [
    new BundleAnalyzerPlugin({
      analyzerMode:
        process.env.OPEN_ANALYZER === 'true' ? 'server' : 'disabled',
      openAnalyzer: process.env.OPEN_ANALYZER === 'true',
    }),

    /**
     * Create global constants which can be configured at compile time.
     *
     * Useful for allowing different behaviour between development builds and
     * release builds
     *
     * NODE_ENV should be production so that modules do not perform certain
     * development checks
     */
    new webpack.EnvironmentPlugin({
      NODE_ENV: 'production',
      DEBUG_PROD: false,
      START_MINIMIZED: false,
      // API URLs
      SHOPIFY_BASE_URL:
        process.env.SHOPIFY_BASE_URL ||
        'https://knotheory-dev.myshopify.com/admin/api/2023-07',
      SHOPIFY_ADMIN_URL:
        process.env.SHOPIFY_ADMIN_URL ||
        'https://admin.shopify.com/store/knotheory-dev',
      ETSY_OAUTH_BASE_URL:
        process.env.ETSY_OAUTH_BASE_URL || 'https://www.etsy.com/oauth/connect',
      ETSY_API_BASE_URL:
        process.env.ETSY_API_BASE_URL ||
        'https://api.etsy.com/v3/public/oauth/token',
      ETSY_REDIRECT_URI: process.env.ETSY_REDIRECT_URI || '',
      CHITCHATS_API_BASE_URL:
        process.env.CHITCHATS_API_BASE_URL || 'https://chitchats.com/api/v1',
    }),
  ],

  /**
   * Disables webpack processing of __dirname and __filename.
   * If you run the bundle in node.js it falls back to these values of node.js.
   * https://github.com/webpack/webpack/issues/2010
   */
  node: {
    __dirname: false,
    __filename: false,
  },
});
