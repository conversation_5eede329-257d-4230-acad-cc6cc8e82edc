{
  "compilerOptions": {
    "target": "ES2018",
    "module": "CommonJS",
    "lib": ["dom", "esnext"],
    "declaration": true,
    "declarationMap": true,
    "noEmit": true,
    "jsx": "react",
    "strict": true,
    "pretty": true,
    "sourceMap": true,
    /* Additional Checks */
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    /* Module Resolution Options */
    "moduleResolution": "node",
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "resolveJsonModule": true,
    "allowJs": true,
    "skipLibCheck": true
  },
  "exclude": [
    "test",
    "release",
    "src/main.prod.js",
    "src/renderer.prod.js",
    "src/dist",
    ".erb/dll"
  ]
}
